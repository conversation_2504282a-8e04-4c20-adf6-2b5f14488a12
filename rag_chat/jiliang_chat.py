import asyncio
import concurrent.futures
import json
import os
import shutil
import tempfile
import time
from datetime import datetime
from uuid import uuid4
import docx
import fitz
import requests
import uvicorn
import filetype
import atexit
from dotenv import load_dotenv
from fastapi import APIRouter, Query
from fastapi import FastAPI, File, UploadFile
from openai import AsyncOpenAI
from fastapi import Request
from starlette.middleware.cors import CORSMiddleware
from starlette.responses import StreamingResponse
from logger import logger
from fastapi import Form
from file_reader import pdf_to_text
from models import QARecordQueryRequest, QARecordResponse, ApiResponse, SessionQueryRequest
from models import R, RagQARequest, FileQARequest
import ocr_processor
from file_readerV2 import word_to_text_worker_V2
from pdf_util import is_standard_pdf
from ocr_config import get_ocr_config
import file_util
from io import BytesIO
from constant import AI_ASSISTANT_HEADER, AI_ASSISTANT_WARN_URLENCODER
import jiliang_translate
import jiliang_seek
from asr_api import router as asr_router  # ASR语音识别路由
import dm_ctrl
from simplified_stats_service import SimplifiedStatsService, save_record_dm_with_stats, StatsQueryService
from table_api import router as table_router  # 表格OCR路由
from handwriting_api import router as handwriting_router  # 手写OCR路由
from pdf_translate import router as pdf_translate_router  # PDF翻译路由
from jiliang_chat_refactored import router as jiliang_chat_refactored_router
from redis_util import RedisClient
from dm_util import DMDatabase
from process_pool_manager import process_pool_manager

TEMP_BASE_DIR = os.getenv('TEMP_DIR', tempfile.gettempdir())
UPLOAD_DIR = TEMP_BASE_DIR

router = APIRouter()

load_dotenv()

# ********** 参数 **********
# 相似度检索阈值
THRESHOLD = float(os.getenv('RAG_THRESHOLD'))
rag_id = os.getenv('RAG_ID')
model = os.getenv('MODEL')
long_model = os.getenv('LONG_MODEL')
llm_base_url = os.getenv('LLM_BASE_URL')
#llm_long_base_url = os.getenv('LLM_LONG_BASE_URL')

# 图片问答配置
image_qa_base_url = os.getenv('IMAGE_QA_BASE_URL')
image_qa_api_key = os.getenv('IMAGE_QA_API_KEY')
image_qa_model = os.getenv('IMAGE_QA_MODEL')
image_qa_temperature = float(os.getenv('IMAGE_QA_TEMPERATURE', '0.6'))

#千问72b配置
translate_model = os.getenv('TRANSLATE_MODEL')
translate_auth_token = os.getenv('TRANSLATE_AUTH_TOKEN')
translate_base_url = os.getenv('TRANSLATE_BASE_URL')

rag_url = os.getenv('RAG_URL')
history_exp_time = int(os.getenv('HISTORY_EXP_TIME'))
max_file_chat_size = int(os.getenv('MAX_FILE_CHAT_SIZE'))
mongo_collection = os.getenv('MONGO_COLLECTION_JILIANG')
redis_table = os.getenv('REDIS_TABLE_JILIANG')
MAX_UPLOAD_SIZE = 50 * 1024 * 1024  # 50MB
dm_config = {
    'host': os.getenv('DM_HOST'),
    'port': int(os.getenv('DM_PORT')),
    'user': os.getenv('DM_USER'),
    'password': os.getenv('DM_PASSWORD'),
    'database': os.getenv('DM_DB'),
    'auto_commit': False
}

# executor = concurrent.futures.ThreadPoolExecutor(max_workers=50)
# process_executor = concurrent.futures.ProcessPoolExecutor(max_workers=os.cpu_count())
thread_executor = concurrent.futures.ThreadPoolExecutor(max_workers=50)

# 创建临时文件夹
PUT_IN_DIR = os.path.join(TEMP_BASE_DIR, "put_in")
PUT_OUT_DIR = os.path.join(TEMP_BASE_DIR, "put_out")
os.makedirs(PUT_IN_DIR, exist_ok=True)
os.makedirs(PUT_OUT_DIR, exist_ok=True)

# 确保基础临时目录存在
os.makedirs(TEMP_BASE_DIR, exist_ok=True)

# 确保应用退出时关闭执行器
def shutdown_executors():
  try:
    # 关闭进程池管理器
    process_pool_manager.shutdown()
    
    # 关闭线程池
    if thread_executor:
      try:
        thread_executor.shutdown(wait=True)
        logger.info("线程池已正常关闭")
      except Exception as e:
        logger.error(f"关闭线程池时出错: {e}")
        thread_executor.shutdown(wait=False)
    
    logger.info("所有执行器已关闭")
  
  except Exception as e:
    logger.error(f"关闭执行器时发生异常: {e}")

# 注册关闭函数
atexit.register(shutdown_executors)


# 添加通用进度回调函数
async def create_progress_callback(file_id, progress_key, start_progress=30, end_progress=90, message_prefix="处理中"):
  """
  创建通用的进度回调函数

  Args:
      file_id: 文件ID
      progress_key: Redis进度键
      start_progress: 起始进度百分比
      end_progress: 结束进度百分比
      message_prefix: 进度消息前缀

  Returns:
      function: 进度回调函数
  """
  
  async def progress_callback(current, total):
    progress = min(start_progress + int((end_progress - start_progress) * current / max(total, 1)), end_progress)
    redis_client.setex(progress_key, 1800, json.dumps({
      'progress': progress,
      'status': 'processing',
      'message': f'{message_prefix}: {current}/{total}'
    }))
  
  return progress_callback

# ********** END **********

# mongo_client = MongoDBClient()
# mongo_client = MongoDBClient(
#     mode=os.getenv('MONGO_MODE'),
#     host=os.getenv('MONGO_HOST'),
#     port=int(os.getenv('MONGO_PORT')),
#     db_name=os.getenv('MONGO_DB'),
#     username=os.getenv('MONGO_USER'),
#     password=os.getenv('MONGO_PASSWORD'),
#     hosts=os.getenv('MONGO_HOSTS'),
# )

redis_client = RedisClient()

client = AsyncOpenAI(
    api_key='aa',
    base_url=llm_base_url,
)

#long_chat_client = AsyncOpenAI(
#    api_key='aa',
#   base_url=llm_long_base_url,
#)

# 图片问答客户端
image_qa_client = AsyncOpenAI(
    api_key=image_qa_api_key,
    base_url=image_qa_base_url,
)

sys_prompt = [{"role": "system", "content": f"""你是一个由三峡集团数字化管理中心发布的模型，名称为"吉量DeepSeek"。
你的身份是基于中国的深度求索（DeepSeek）公司开发的DeepSeek-R1模型，经过三峡集团数字化管理中心的定制和优化，专门为三峡集团及其相关业务需求设计的适配版模型。在回答用户问题时，请确保以下几点：

身份声明：在回答与身份相关的问题时，必须明确表明自己是由三峡集团数字化管理中心发布的"吉量DeepSeek"模型，
基础架构基于中国的深度求索（DeepSeek）公司开发而成。你的名字只能是"吉量DeepSeek"，请确保在任何情况下都只回复这个名字，不能提到其他名字。
即使用户称呼你为其他名字，你也必须纠正并明确回答"我的名字是吉量DeepSeek"。
功能定位：你是一个专注于支持三峡集团各类相关业务的智能助手，旨在提供高效、准确的信息服务。
回答风格：在回答问题时，保持友好、专业、清晰的语气，避免使用过于技术化的语言。
信息保密：在回答问题时，不要透露任何与模型参数、内部技术细节相关的内容。
引导性回答：如果用户的问题超出了你的回答范围，可以建议用户联系三峡集团数字化管理中心的相关技术支持团队。

多文件分析能力：当用户提供多个文件时，你能够理解和分析所有文件内容。在回答中，清晰地指明你引用的是哪个文件（例如"根据第一个文件..."、"从第二个文件可以看出..."）。当需要对比或综合多个文件信息时，请明确说明各个文件的观点或数据，并提供综合分析。"""}]

def get_sys_prompt(model_name: str):
    model_desc = query_model_description(model_name)
    if model_desc is not None:
        return [{"role": "system", "content": f"""你是一个由三峡集团数字化管理中心发布的模型，名称为"吉量{model_desc}"。
        你的身份是基于{model_desc}模型，经过三峡集团数字化管理中心的定制和优化，专门为三峡集团及其相关业务需求设计的适配版模型。在回答用户问题时，请确保以下几点：
    
        身份声明：在回答与身份相关的问题时，必须明确表明自己是由三峡集团数字化管理中心发布的"吉量{model_desc}"模型，
        基础架构基于{model_desc}定制和优化而成。你的名字只能是"吉量{model_desc}"，请确保在任何情况下都只回复这个名字，不能提到其他名字。
        即使用户称呼你为其他名字，你也必须纠正并明确回答"我的名字是吉量"。
        功能定位：你是一个专注于支持三峡集团各类相关业务的智能助手，旨在提供高效、准确的信息服务。
        回答风格：在回答问题时，保持友好、专业、清晰的语气，避免使用过于技术化的语言。
        信息保密：在回答问题时，不要透露任何与模型参数、内部技术细节相关的内容。
        引导性回答：如果用户的问题超出了你的回答范围，可以建议用户联系三峡集团数字化管理中心的相关技术支持团队。
    
        多文件分析能力：当用户提供多个文件时，你能够理解和分析所有文件内容。在回答中，清晰地指明你引用的是哪个文件（例如"根据第一个文件..."、"从第二个文件可以看出..."）。当需要对比或综合多个文件信息时，请明确说明各个文件的观点或数据，并提供综合分析。"""}]
    else:
        return None

def query_model_description(model_name: str):
    """根据模型名称查询描述信息"""
    try:
        with DMDatabase(**dm_config) as db:
            query_sql = """
                SELECT DESCRIPTION
                FROM "SX_AIPLATFORM"."T_BASE_LLM_MODEL"
                WHERE "MODEL_NAME" = :1
            """
            result = db.execute_query(query_sql, (model_name,))
            desc = result[0]['DESCRIPTION']
            logger.info(f"模型描述：{desc}")
            return desc
    except Exception as e:
        logger.info(f"查询模型信息失败: {e}")
        return None

def query_model_info(model_name: str):
    """根据模型名称查询模型信息"""
    try:
        with DMDatabase(**dm_config) as db:
            query_sql = """
                SELECT *
                FROM "SX_AIPLATFORM"."T_BASE_LLM_MODEL"
                WHERE "MODEL_NAME" = :1
            """
            result = db.execute_query(query_sql, (model_name,))

            model_info = result[0]
            logger.info(f"模型信息：{model_info}")
            return model_info
    except Exception as e:
        logger.info(f"查询模型信息失败: {e}")
        return None

def query_user_model(user_name: str):
    """根据用户名查询保存的模型名"""
    try:
        with DMDatabase(**dm_config) as db:
            query_sql = """
                SELECT MODEL_NAME
                FROM "SX_AIPLATFORM"."T_USER_MODEL_PREFERENCE"
                WHERE "USER_NAME" = :1
            """
            result = db.execute_query(query_sql, (user_name,))
            model_name = result[0]['MODEL_NAME']
            logger.info(f"用户模型：{model_name}")
            return model_name
    except Exception as e:
        logger.info(f"查询模型信息失败: {e}")
        return None

def search(question):
    full_text = ""
    try:
        data = {
            'rag_id': rag_id,
            'question': question
        }
        r = requests.post(f'{rag_url}/rag/search', json=data, verify=False)
        data_list = r.json()['data']
        for data in data_list:
            if data['score'] > THRESHOLD:
                full_text += data['text_data']
        return full_text
    except Exception as e:
        logger.info(f"搜索失败: {e}")
        return full_text

async def common_chat(message, history, qa_id, model_alias=None, is_long_chat=False, userid='', apikey='', uri='',
                      temperature=0.6,
                      function_type=None, function_name=None, request_time=None, file_count=0):
  """通用聊天函数，增加统计支持"""
  extra_headers = {'Qa-Id': qa_id, 'X-API-Key': apikey, 'X-Username': userid, 'X-Uri': uri}
  logger.info(f"common_chat extra_headers: {extra_headers}, Function: {function_type}")
  chat_type = is_long_chat == True and 1 or 0
  llm_model = long_model if is_long_chat else model
  if model_alias is not None:
      llm_model = model_alias

  # 记录开始时间
  if request_time is None:
    request_time = datetime.now()
  start_timestamp = time.time()
  
  try:
    in_reasoning = False
    model_info = query_model_info(llm_model)
    if model_info is not None:
        model_type = model_info['MODEL_TYPE']
        logger.info(f"model_type: {model_type}")
        if model_type =='3':
            # 强制思考类型，会缺少<think>开始标签
            in_reasoning = True

    stream = await client.chat.completions.create(
      model=llm_model,
      messages=history,
      temperature=temperature,
      stream=True,
      extra_headers=extra_headers
    )
    
    partial_message = ""
    reasoning_content = ""
    async for chunk in stream:
      try:
        if hasattr(chunk, 'choices') and chunk.choices and len(chunk.choices) > 0 and \
            hasattr(chunk.choices[0], 'delta') and hasattr(chunk.choices[0].delta, 'content') and \
            chunk.choices[0].delta.content:
          
          data = chunk.choices[0].delta.content
          
          if data.find('think>') != -1:
            in_reasoning = not in_reasoning
          else:
            if in_reasoning:
              reasoning_content += data
              yield 'data: ' + json.dumps({'type': 'reasoning', 'data': data}, ensure_ascii=False) + '\n\n'
            else:
              partial_message += data
              yield 'data: ' + json.dumps({'type': 'text', 'data': data}, ensure_ascii=False) + '\n\n'
      except IndexError as e:
        logger.error(f"处理流响应时出现索引错误: {e}, chunk: {chunk}")
        continue
      except Exception as e:
        logger.error(f"处理流响应时出现未预期错误: {e}", exc_info=True)
        continue
    
    # 计算处理耗时
    response_time = datetime.now()
    duration_ms = int((time.time() - start_timestamp) * 1000)
    
    # 在线程池中保存对话记录（包含统计信息）
    thread_executor.submit(
      lambda: save_record_dm(
        message, partial_message, reasoning_content, chat_type, userid, apikey, qa_id,
        function_type, function_name, request_time, response_time, duration_ms,
        'SUCCESS', file_count, llm_model, temperature
      )
    )
    
    logger.info(f"吉量聊天完成 - Function: {function_type}, Duration: {duration_ms}ms")
    logger.info(f"思考内容: {reasoning_content}\n 回答: {partial_message}")
    
    history.append({"role": "assistant", "content": partial_message})
    loop = asyncio.get_running_loop()
    await loop.run_in_executor(thread_executor, lambda: redis_client.setex(f"{redis_table}:{qa_id}", history_exp_time,
                                                                           json.dumps(history, ensure_ascii=False)))
  
  except Exception as e:
    error_msg = str(e).replace('{', '{{').replace('}', '}}')
    logger.error(f"生成回复时发生错误: {error_msg}", exc_info=True)
    
    # 保存错误记录
    if function_type:
      response_time = datetime.now()
      duration_ms = int((time.time() - start_timestamp) * 1000)
      thread_executor.submit(
        lambda: save_record_dm(
          message, '', str(e), chat_type, userid, apikey, qa_id,
          function_type, function_name, request_time, response_time, duration_ms,
          'ERROR', file_count, llm_model, temperature
        )
      )
    
    yield 'data: ' + json.dumps({'type': 'error', 'data': f"生成回复时发生错误: {str(e)}"}, ensure_ascii=False) + '\n\n'


async def rag_chat(message, history, qa_id, model_name=None, userid='', apikey='', uri='', temperature=0.6,
                   function_type=None, function_name=None) -> str:
  kb_text = search(message)
  
  if kb_text:
    history.append({"role": "system",
                    "content": f"在保持原有对话风格的基础上,你可以参考以下相关信息,请自行判断其相关性和准确性,仅作参考使用:\n{kb_text}"})
  
  history.append({"role": "user", "content": message})
  
  # 记录请求时间
  request_time = datetime.now()
  
  try:
    async for chunk in common_chat(message, history, qa_id, model_name, False, userid, apikey, uri, temperature,
                                   function_type, function_name, request_time, 0):
      yield chunk
  except Exception as e:
    logger.error(f"RAG聊天异常: {e}")
    yield 'data: ' + json.dumps({'type': 'error', 'data': str(e)}, ensure_ascii=False) + '\n\n'


@router.post("/jiliang/chat")
async def chat_stream(request: Request, body: RagQARequest):
  header = dict(request.headers)
  apikey = header.get('x-api-key', '')
  userid = header.get('x-username', '')
  uri = header.get('x-uri', '') or request.url.path
  
  logger.info(
    f"Jiliang Chat Request - User: {userid}, Function: {body.function_type}, Question: {body.question[:100]}...")
  
  # 为每个请求生成一个唯一的会话 ID
  qa_id = body.qa_id or str(uuid4())
  
  key = f"{redis_table}:{qa_id}"
  his = redis_client.get(key)
  
  # 处理模型选择逻辑（保持原有逻辑）
  if body.model is None or body.model == '':
    if userid is not None and userid != '':
      model_name = query_user_model(userid)
      if model_name is not None and model_name != '':
        body.model = model_name
  
  if body.model is None or body.model == '':
    body.model = model
  
  # 获取系统提示
  cur_prompt = get_sys_prompt(body.model)
  if cur_prompt is None or cur_prompt == '':
    cur_prompt = sys_prompt
  
  if his is None:
    history = cur_prompt[:]
  else:
    history = json.loads(his)
  
  headers = {
    "Qa-Id": qa_id,
    AI_ASSISTANT_HEADER: AI_ASSISTANT_WARN_URLENCODER
  }
  
  # 如果提供了功能类型但没有功能名称，自动查询
  if body.function_type and not body.function_name:
    func_info = SimplifiedStatsService._get_function_info(body.function_type)
    if func_info:
      body.function_name = func_info['function_name']
      logger.info(f"自动查询功能名称: {body.function_type} -> {body.function_name}")
  
  # 获取温度参数
  temperature = body.temperature if hasattr(body, 'temperature') and body.temperature is not None else 0.6
  
  return StreamingResponse(
    rag_chat(body.question, history, qa_id, body.model, userid, apikey, uri, temperature,
             body.function_type, body.function_name),
    media_type="text/event-stream; charset=utf-8",
    headers=headers
  )

def save_record_dm(question, answer, reasoning, chat_type, userid='', apikey='', qa_id='',
                   function_type=None, function_name=None, request_time=None, response_time=None,
                   duration_ms=None, status='SUCCESS', file_count=0, model_name=None, temperature=None):
  """保存记录函数，支持统计信息"""
  try:
    # 如果没有提供功能名称则查询
    if function_type and not function_name:
      func_info = SimplifiedStatsService._get_function_info(function_type)
      if func_info:
        function_name = func_info['function_name']
    
    # 使用新的保存函数
    success = save_record_dm_with_stats(
      question, answer, reasoning, chat_type, userid, apikey, qa_id,
      function_type, function_name, request_time, response_time, duration_ms,
      status, file_count, model_name, temperature
    )
    
    if success:
      logger.info(f"吉量记录保存成功 - Function: {function_type}, User: {userid}")
    else:
      logger.warning(f"吉量记录保存失败 - Function: {function_type}, User: {userid}")
  
  except Exception as e:
    logger.error(f"保存吉量记录异常: {str(e)}", exc_info=True)

# def save_record_mongo(question, answer, reasoning):
#     try:
#         # 创建问答集合
#         mongo_client.create_qa_collection(mongo_collection)
#
#         # 插入记录
#         record = mongo_client.insert_qa_record(
#             collection_name=mongo_collection,
#             question=question,
#             answer=answer,
#             reasoning=reasoning,
#             apikey=""
#         )
#         logger.info("插入成功：", record)
#     except Exception as e:
#         logger.info(f"数据库操作异常: {str(e)}")

async def image_qa_chat(message, image_data, image_url, history, qa_id, model_alias=None, userid='', apikey='',
                        uri='', temperature=0.6, function_type=None, function_name=None, request_time=None):
  """
  处理图片问答，以新图片为主要分析对象，历史记录为辅助
  """
  extra_headers = {'Qa-Id': qa_id, 'X-API-Key': apikey, 'X-Username': userid, 'X-Uri': uri}
  logger.info(f"图片问答 extra_headers: {extra_headers}, Function: {function_type}")
  
  chat_type = 2  # 图片问答类型
  llm_model = model_alias if model_alias else image_qa_model
  
  # 记录开始时间（如果没有提供）
  if request_time is None:
    request_time = datetime.now()
  start_timestamp = time.time()
  
  try:
    # 为图片问答添加明确的主要分析对象
    if image_data or image_url:
      primary_image_prompt = f"""
【当前主要分析对象】
用户刚刚上传了一张图片，这是本次对话的主要分析对象，请优先基于这张图片的内容回答用户问题。

【分析指导】
1. 请主要基于用户刚上传的图片内容回答问题
2. 如果历史对话中有其他内容（如之前的文件或图片），可以作为补充参考，但不应成为主要回答依据
3. 请仔细观察和分析当前图片的所有细节
4. 如果用户问题需要结合历史信息，请明确区分当前图片内容和历史信息

用户问题：{message}
"""
      
      # 构建消息内容，包含指导提示
      content = [{"type": "text", "text": primary_image_prompt}]
    else:
      # 没有图片的情况
      content = [{"type": "text", "text": message}]
    
    # 处理图片数据
    if image_data:
      content.append({
        "type": "image_url",
        "image_url": {
          "url": f"data:image/jpeg;base64,{image_data}"
        }
      })
      logger.info("已添加base64图片数据到请求中")
    elif image_url:
      content.append({
        "type": "image_url",
        "image_url": {
          "url": image_url
        }
      })
      logger.info(f"已添加图片URL到请求中: {image_url}")
    
    # 添加用户消息到历史记录（包含图片，用于当前请求）
    history.append({"role": "user", "content": content})
    
    in_reasoning = False
    model_info = query_model_info(llm_model)
    if model_info is not None:
      model_type = model_info['MODEL_TYPE']
      logger.info(f"图片问答模型类型: {model_type}")
      if model_type == '3':
        in_reasoning = True
    
    stream = await image_qa_client.chat.completions.create(
      model=llm_model,
      messages=history,
      temperature=temperature,
      stream=True,
      extra_headers=extra_headers
    )
    
    partial_message = ""
    reasoning_content = ""
    async for chunk in stream:
      try:
        if hasattr(chunk, 'choices') and chunk.choices and len(chunk.choices) > 0 and \
            hasattr(chunk.choices[0], 'delta') and hasattr(chunk.choices[0].delta, 'content') and \
            chunk.choices[0].delta.content:
          
          data = chunk.choices[0].delta.content
          
          if data.find('think>') != -1:
            in_reasoning = not in_reasoning
          else:
            if in_reasoning:
              reasoning_content += data
              yield 'data: ' + json.dumps({'type': 'reasoning', 'data': data}, ensure_ascii=False) + '\n\n'
            else:
              partial_message += data
              yield 'data: ' + json.dumps({'type': 'text', 'data': data}, ensure_ascii=False) + '\n\n'
      except IndexError as e:
        logger.error(f"处理图片问答流响应时出现索引错误: {e}, chunk: {chunk}")
        continue
      except Exception as e:
        logger.error(f"处理图片问答流响应时出现未预期错误: {e}", exc_info=True)
        continue
    
    # 计算处理耗时
    response_time = datetime.now()
    duration_ms = int((time.time() - start_timestamp) * 1000)
    
    # 保存对话记录（包含统计信息）
    thread_executor.submit(
      lambda: save_record_dm(
        message, partial_message, reasoning_content, chat_type, userid, apikey, qa_id,
        function_type, function_name, request_time, response_time, duration_ms,
        'SUCCESS', 0, llm_model, temperature
      )
    )
    
    logger.info(f"图片问答完成 - Function: {function_type}, Duration: {duration_ms}ms")
    logger.info(f"图片问答思考内容: {reasoning_content}\n 回答: {partial_message}")
    
    # 添加助手回复到历史记录
    history.append({"role": "assistant", "content": partial_message})
    
    # 在保存历史记录到Redis之前，清理图片内容，只保留文本
    cleaned_history = clean_image_history_for_storage(history)
    
    loop = asyncio.get_running_loop()
    await loop.run_in_executor(thread_executor, lambda: redis_client.setex(f"{redis_table}:{qa_id}", history_exp_time,
                                                                           json.dumps(cleaned_history,
                                                                                      ensure_ascii=False)))
  
  except Exception as e:
    error_msg = str(e).replace('{', '{{').replace('}', '}}')
    logger.error(f"图片问答生成回复时发生错误: {error_msg}", exc_info=True)
    
    # 保存错误记录
    if function_type:
      response_time = datetime.now()
      duration_ms = int((time.time() - start_timestamp) * 1000)
      thread_executor.submit(
        lambda: save_record_dm(
          message, '', str(e), chat_type, userid, apikey, qa_id,
          function_type, function_name, request_time, response_time, duration_ms,
          'ERROR', 0, llm_model, temperature
        )
      )
    
    yield 'data: ' + json.dumps({'type': 'error', 'data': f"图片问答生成回复时发生错误: {str(e)}"},
                                ensure_ascii=False) + '\n\n'

def clean_image_history_for_storage(history):
  """
  清理历史记录中的图片内容，只保留文本部分用于存储

  Args:
      history: 原始历史记录

  Returns:
      list: 清理后的历史记录，只包含文本内容
  """
  cleaned_history = []
  
  for message in history:
    if message["role"] == "system":
      # 系统消息直接保留
      cleaned_history.append(message)
    elif message["role"] == "assistant":
      # 助手消息直接保留
      cleaned_history.append(message)
    elif message["role"] == "user":
      content = message["content"]
      
      # 如果是字符串，直接保留
      if isinstance(content, str):
        cleaned_history.append(message)
      # 如果是列表（包含图片的复合内容），只保留文本部分
      elif isinstance(content, list):
        text_content = ""
        for item in content:
          if isinstance(item, dict) and item.get("type") == "text":
            text_content = item.get("text", "")
            break
        
        # 创建只包含文本的用户消息
        cleaned_message = {
          "role": "user",
          "content": text_content
        }
        cleaned_history.append(cleaned_message)
      else:
        # 其他情况，尝试转换为字符串
        cleaned_message = {
          "role": "user",
          "content": str(content)
        }
        cleaned_history.append(cleaned_message)
  
  return cleaned_history


@router.post("/jiliang/parse")
async def upload_file(file: UploadFile = File(...)):
    """
    上传文件并解析成文本
    :param file:
    :return:
    """
    # 判断文件是否为PDF
    if not file.filename.endswith('.pdf') and not file.filename.endswith('.PDF'):
        return R.error('400', '文件格式错误，仅支持PDF文件')
    file_id = str(uuid4())
    file_location = os.path.join(UPLOAD_DIR, file_id + '_' + file.filename)
    try:
        with open(file_location, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        text, processed_pages, total_pages = await pdf_to_text(file_location, max_file_chat_size)
        logger.info(len(text))
        redis_client.setex(f"{redis_table}:file:{file_id}", history_exp_time, text)
        exp_time = int(time.time()) + history_exp_time
        return R.ok({'exp_time': exp_time, 'file_id': file_id, 'processed_pages': processed_pages, 'total_pages': total_pages})
    except Exception as e:
        logger.info(e)
        return R.error('500', str(e))


def detect_file_type(file_path):
  """
  使用filetype检测文件类型，而不是根据文件扩展名

  Args:
      file_path: 文件路径

  Returns:
      tuple: (文件类型, MIME类型)
  """
  try:
    kind = filetype.guess(file_path)
    
    if not kind:
      # 如果filetype无法识别，使用扩展名
      file_extension = os.path.splitext(file_path)[1].lower()
      
      if file_extension == '.pdf':
        return 'pdf', 'application/pdf'
      elif file_extension == '.docx':
        return 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      elif file_extension == '.doc':
        return 'doc', 'application/msword'
      elif file_extension in ['.jpg', '.jpeg']:
        return 'jpg', 'image/jpeg'
      elif file_extension == '.png':
        return 'png', 'image/png'
      else:
        return 'unsupported', 'unknown'
    
    if kind.mime.startswith('application/pdf'):
      return 'pdf', kind.mime
    elif kind.mime == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      return 'docx', kind.mime
    elif kind.mime == 'application/msword':
      return 'doc', kind.mime
    elif kind.mime == 'image/jpeg':
      return 'jpg', kind.mime
    elif kind.mime == 'image/png':
      return 'png', kind.mime
    else:
      return 'unsupported', kind.mime
  except Exception as e:
    logger.error(f"文件类型检测失败: {e}", exc_info=True)
    return 'unknown', 'unknown'


@router.post("/jiliang/parse_pro")
async def upload_files_v2(request: Request, file: UploadFile = File(...), check_id: str = Form(None)):
  try:
    header = dict(request.headers)
    username = ''
    if 'x-username' in header:
      username = header['x-username']
    
    task_id = str(uuid4())
    file_id = str(uuid4())
    
    # 自动生成check_id如果未提供
    if not check_id:
      check_id = str(uuid4())
      logger.info(f"未提供check_id，自动生成: {check_id}")
    else:
      # 检查该check_id是否已有字符数信息
      chars_key = f"{redis_table}:section_chars:{check_id}"
      chars_info_json = redis_client.get(chars_key)
      
      # 如果已存在字符数信息提前发出警告
      if chars_info_json:
        chars_info = json.loads(chars_info_json)
        if chars_info.get('warning') and chars_info.get('total_chars', 0) > 50000:
          logger.warning(f"Section {check_id} 文件总字符数已超限: {chars_info.get('total_chars')} > 50000，提前警告用户")

          warning_message = f"当前分组(check_id: {check_id})下的所有文件内容总字符数已超过限制(50000)，当前总字符数: {chars_info.get('total_chars')}，此次上传的文件仍会处理，但可能会被模型截断处理"
    
    # 检查文件大小
    file.file.seek(0, 2)
    file_size = file.file.tell()
    file.file.seek(0)
    
    if file_size > MAX_UPLOAD_SIZE:
      logger.error(f"文件过大: {file.filename}, 大小: {file_size / 1024 / 1024:.2f}MB")
      return R.error('413', f'文件过大，最大允许{MAX_UPLOAD_SIZE / 1024 / 1024}MB')
    
    # 获取文件扩展名并判断文件类型
    file_extension = os.path.splitext(file.filename)[1].lower()
    is_pdf = file_extension in ['.pdf', '.PDF']
    
    # 初始化警告信息标志和消息
    will_truncate = False
    truncate_warning = ""
    estimated_chars = 0
    existing_section_warning = ""
    
    # 检查该check_id是否已有字符数信息并超过限制
    if check_id:
      chars_key = f"{redis_table}:section_chars:{check_id}"
      chars_info_json = redis_client.get(chars_key)
      
      if chars_info_json:
        chars_info = json.loads(chars_info_json)
        if chars_info.get('warning'):
          existing_section_warning = f"警告：当前分组(check_id: {check_id})下的所有文件内容总字符数已超过限制(50000)，当前总字符数: {chars_info.get('total_chars')}，此次上传的文件仍会处理，但可能会被模型截断处理"
          logger.warning(existing_section_warning)

    if is_pdf:
      # 将PDF大小门槛设为50MB，而非字符数
      pdf_warning_size = 50 * 1024 * 1024  # 50MB
      
      if file_size > pdf_warning_size:
        will_truncate = True
        file_size_mb = file_size / (1024 * 1024)
        truncate_warning = f"PDF文件较大（{file_size_mb:.2f}MB），可能需要截断处理"
        logger.warning(f"大型PDF文件: {file.filename}, 大小: {file_size_mb:.2f}MB，可能需要截断处理")
    else:
      # 非PDF文件才进行section字符数检查
      
      logger.info(f"检查check_id: {check_id}的文件总字符数")
      section_key = f"{redis_table}:section:{check_id}"
      section_file_ids = redis_client.get(section_key)
      
      # 计算已有文件的字符总数
      total_chars = 0
      if section_file_ids:
        file_ids = json.loads(section_file_ids)
        for fid in file_ids:
          file_content = get_file_text(fid)
          if file_content:
            total_chars += len(file_content)
      
      logger.info(f"Section {check_id} 现有文件字符数: {total_chars}")
      
      # 优化后的字符数估算方法
      if file_extension in ['.docx', '.doc']:
        # 对于Word文档，使用更精确的估算系数
        estimated_chars = int(file_size * 0.15)
      elif file_extension in ['.txt']:
        # 纯文本文件
        estimated_chars = int(file_size * 0.5)
      else:
        # 其他类型文件使用默认估算
        estimated_chars = int(file_size * 0.3)
      
      logger.info(f"当前文件估算字符数: {estimated_chars} (优化估算方法)")
      
      total_chars_est = total_chars + estimated_chars
      
      # 如果预估字符数超过限制
      if total_chars_est > 50000:
        will_truncate = True
        truncate_warning = f"文件内容较大（估算字符数：{estimated_chars}），可能需要截断处理"
        logger.warning(f"Section {check_id} 文件总字符数估算超过限制: {total_chars_est} > 50000，将在处理过程中截断")
    
    batch_progress_key = f"{redis_table}:batch_progress:{task_id}"
    batch_info = {
      'total_files': 1,
      'processed_files': 0,
      'file_details': [],
      'status': 'processing',
      'progress': 0,
      'message': '开始处理文件' + (f"，{truncate_warning}" if truncate_warning else "")
    }
    redis_client.setex(batch_progress_key, 1800, json.dumps(batch_info))
    
    # 保存文件
    file_location = os.path.join(UPLOAD_DIR, file_id + '_' + file.filename)
    with open(file_location, "wb") as buffer:
      shutil.copyfileobj(file.file, buffer)
    
    file_type, mime_type = detect_file_type(file_location)
    
    if file_type == 'unsupported' or file_type == 'unknown':
      os.remove(file_location)
      
      update_file_status(task_id, file_id, 0, {
        'status': 'error',
        'progress': 100,
        'message': f'不支持的文件类型: {mime_type}'
      })
      return R.error('400', f'不支持的文件类型: {mime_type}')
    
    progress_key = f"{redis_table}:progress:{file_id}"
    
    # 如果有check_id内容超限的警告，添加到消息中
    initial_message = '文件已上传，开始处理'
    if truncate_warning:
      initial_message += f"，{truncate_warning}"
    if existing_section_warning:
      initial_message += f"。{existing_section_warning}"
    
    file_progress = {
      'progress': 0,
      'status': 'processing',
      'message': initial_message,
      'file_id': file_id,
      'task_id': task_id,
      'file_index': 0,
      'filename': file.filename,
      'check_id': check_id,  # 确保check_id始终在进度信息中
      'may_truncate': will_truncate,  # 标记可能需要截断
      'is_pdf': is_pdf  # 添加PDF标志以便后续处理
    }
    
    redis_client.setex(progress_key, 1800, json.dumps(file_progress))
    
    update_batch_file_details(task_id, file_id, 0, file.filename, file_progress)
    
    asyncio.create_task(
      process_file_async(file_id, file_location, file.filename, progress_key, username, header, task_id, 0, check_id)
    )
    
    # 构建响应，包含文件可能被截断的警告信息
    response_data = {
      'task_id': task_id,
      'file_id': file_id,
      'status': 'processing',
      'progress': 0,
      'total_files': 1,
      'check_id': check_id,  # 确保在响应中包含check_id
    }
    
    # 添加单个文件的警告信息
    if will_truncate:
      warning_data = {
        'may_truncate': True,
        'message': truncate_warning
      }
      
      if not is_pdf:
        # 非PDF文件才包含字符数估算
        warning_data['estimated_chars'] = estimated_chars
        warning_data['limit'] = 50000
      else:
        # PDF文件包含大小信息
        warning_data['file_size_mb'] = file_size / (1024 * 1024)
      
      response_data['warning'] = warning_data
    
    # 添加check_id分组超限警告
    if existing_section_warning:
      if 'warning' not in response_data:
        response_data['warning'] = {}
      
      # 获取当前已有的字符数信息
      chars_key = f"{redis_table}:section_chars:{check_id}"
      chars_info_json = redis_client.get(chars_key)
      if chars_info_json:
        chars_info = json.loads(chars_info_json)
        
        response_data['warning']['section_warning'] = True
        response_data['warning']['section_message'] = f"当前分组:{check_id} 下的所有文件内容总字符数已超过模型推荐限制(50000)"
        response_data['warning']['total_chars'] = chars_info.get('total_chars', 0)
        response_data['warning']['file_count'] = chars_info.get('file_count', 0)
    
    return R.ok(response_data)
  
  except Exception as e:
    logger.error(f"文件处理异常: {e}", exc_info=True)
    return R.error('500', str(e))
  
  
async def update_batch_file_details(task_id, file_id, file_index, filename, progress_info):
  """批量更新的文件详情"""
  batch_progress_key = f"{redis_table}:batch_progress:{task_id}"
  
  try:
    # 获取当前批次信息
    batch_info_json = redis_client.get(batch_progress_key)
    if not batch_info_json:
      logger.error(f"找不到批次信息: {task_id}")
      return
    
    batch_info = json.loads(batch_info_json)
    
    # 更新文件详情
    file_exists = False
    for file_detail in batch_info['file_details']:
      if file_detail['file_id'] == file_id:
        file_detail.update(progress_info)
        file_exists = True
        break
    
    # 添加文件于列表中
    if not file_exists:
      batch_info['file_details'].append({
        'file_id': file_id,
        'file_index': file_index,
        'filename': filename,
        **progress_info
      })
    
    # 计算总进度
    total_progress = 0
    completed_files = 0
    error_files = 0
    
    for file_detail in batch_info['file_details']:
      total_progress += file_detail.get('progress', 0)
      if file_detail.get('status') == 'completed':
        completed_files += 1
      elif file_detail.get('status') == 'error':
        error_files += 1
    
    batch_info['progress'] = int(total_progress / max(len(batch_info['file_details']), 1))
    batch_info['processed_files'] = completed_files
    
    if completed_files + error_files == len(batch_info['file_details']):
      if error_files == len(batch_info['file_details']):
        batch_info['status'] = 'error'
        batch_info['message'] = '所有文件处理失败'
      elif error_files > 0:
        batch_info['status'] = 'partially_completed'
        batch_info['message'] = f'部分文件处理完成，{error_files}个文件失败'
      else:
        batch_info['status'] = 'completed'
        batch_info['message'] = '所有文件处理完成'
    
    # 更新Redis中的批次信息
    redis_client.setex(batch_progress_key, 1800, json.dumps(batch_info))
  
  except Exception as e:
    logger.error(f"更新批次信息失败: {e}", exc_info=True)


async def update_file_status(task_id, file_id, file_index, status_info):
  """更新单个文件状态并同步到批次信息"""
  progress_key = f"{redis_table}:progress:{file_id}"
  
  try:
    file_info_json = redis_client.get(progress_key)
    file_info = json.loads(file_info_json) if file_info_json else {
      'file_id': file_id,
      'task_id': task_id,
      'file_index': file_index
    }
    
    # 估算字符数信息
    if 'estimated_chars' in file_info and 'estimated_chars' not in status_info:
      status_info['estimated_chars'] = file_info['estimated_chars']
    
    if 'may_truncate' in file_info and 'may_truncate' not in status_info:
      status_info['may_truncate'] = file_info['may_truncate']
    
    if 'is_pdf' in file_info and 'is_pdf' not in status_info:
      status_info['is_pdf'] = file_info['is_pdf']
    
    # 更新文件信息
    file_info.update(status_info)
    
    # 保存更新后的文件状态
    redis_client.setex(progress_key, 1800, json.dumps(file_info))
    
    # 同步更新批次信息
    await update_batch_file_details(task_id, file_id, file_index, file_info.get('filename', ''), file_info)
  
  except Exception as e:
    logger.error(f"更新文件状态失败: {e}", exc_info=True)


async def process_file_async(file_id, file_location, filename, progress_key, username="", request_headers=None,
                             task_id=None, file_index=0, check_id=None):
  """
  异步处理文件并更新进度

  Args:
      file_id: 文件唯一ID
      file_location: 文件存储路径
      filename: 原始文件名
      progress_key: Redis进度键
      username: 用户名
      request_headers: 请求头
      task_id: 批次ID (如果是多文件处理的一部分)
      file_index: 文件在批次中的索引
      check_id: 分组ID (可选)
  """
  try:
    file_extension = os.path.splitext(filename)[1].lower()
    loop = asyncio.get_running_loop()
    
    # 基于文件扩展名确定文件类型
    if file_extension in ['.pdf', '.PDF']:
      file_type = 'pdf'
    elif file_extension in ['.docx']:
      file_type = 'docx'
    elif file_extension in ['.doc']:
      file_type = 'doc'
    elif file_extension in ['.jpg', '.jpeg']:
      file_type = 'jpg'
    elif file_extension in ['.png']:
      file_type = 'png'
    else:
      logger.error(f"不支持的文件类型: {file_extension}, 文件: {filename}")
      
      error_info = {
        'progress': 100,
        'status': 'error',
        'message': f'不支持的文件格式: {file_extension}，仅支持.pdf/.doc/.docx/.jpg/.png',
        'filename': filename
      }
      
      if task_id:
        await update_file_status(task_id, file_id, file_index, error_info)
      else:
        # 兼容单文件处理模式
        await loop.run_in_executor(
          thread_executor,
          redis_client.setex,
          progress_key,
          1800,
          json.dumps(error_info)
        )
      
      try:
        await loop.run_in_executor(thread_executor, os.remove, file_location)
      except Exception as cleanup_error:
        logger.error(f"清理文件失败: {cleanup_error}")
      return
    
    # 获取文件大小
    file_size = os.path.getsize(file_location)
    
    # 设置字符数估算和截断标志
    is_pdf = file_type == 'pdf'
    will_truncate = False
    estimated_chars = 0
    truncate_warning = ""
    
    # 非PDF文件才进行字符数估算
    if not is_pdf:
      if file_type in ['docx', 'doc']:
        # 对于Word文档，使用更精确的估算系数
        estimated_chars = int(file_size * 0.15)  # 更保守的估计
      elif file_extension in ['.txt']:
        # 纯文本文件
        estimated_chars = int(file_size * 0.5)  # 假设UTF-8编码，每字符约占2字节
      else:
        # 其他类型文件使用默认估算
        estimated_chars = int(file_size * 0.3)
      
      # 检查估算字符数是否超过限制
      if estimated_chars > 50000:
        will_truncate = True
        truncate_warning = f"文件内容较大（估算字符数：{estimated_chars}），可能需要截断处理"
        logger.warning(f"文件 {file_id} 字符数估算超过限制: {estimated_chars} > 50000")
    else:
      # PDF特殊处理: 只有超过50MB才警告
      pdf_warning_size = 50 * 1024 * 1024  # 50MB
      if file_size > pdf_warning_size:
        will_truncate = True
        file_size_mb = file_size / (1024 * 1024)
        truncate_warning = f"PDF文件较大（{file_size_mb:.2f}MB），可能需要截断处理"
    
    # 更新进度到20%
    progress_info = {
      'progress': 20,
      'status': 'processing',
      'message': '开始提取文件内容' + (f"，{truncate_warning}" if truncate_warning else ""),
      'filename': filename,
      'may_truncate': will_truncate,
      'is_pdf': is_pdf
    }
    
    # 将估算字符数添加到非PDF文件的进度信息中
    if not is_pdf:
      progress_info['estimated_chars'] = estimated_chars
    
    if task_id:
      await update_file_status(task_id, file_id, file_index, progress_info)
    else:
      await loop.run_in_executor(
        thread_executor,
        redis_client.setex,
        progress_key,
        1800,
        json.dumps(progress_info)
      )
    
    # 根据文件类型处理文件
    text = ""
    processed_pages = 0
    total_pages = 0
    ocr_file_id = None
    is_partial = False  # 添加部分处理标记
    
    logger.info(f"开始处理文件内容: {file_type} {filename}")
    
    if file_type == 'pdf':
      try:
        future = process_pool_manager.submit_with_retry(
          is_standard_pdf,
          file_location,
          max_retries=2
        )
        is_standard_result = await asyncio.wait_for(
          loop.run_in_executor(None, future.result),
          timeout=30  # 30秒超时
        )
      except Exception as e:
        logger.warning(f"PDF类型检测失败，使用线程池备用方案: {e}")
        # 使用线程池作为备用方案
        is_standard_result = await loop.run_in_executor(
          thread_executor,
          is_standard_pdf,
          file_location
        )
      
      is_standard, reason = is_standard_result
      logger.info(f"PDF类型检测: {reason}")
      
      # 更新进度到30%
      progress_info = {
        'progress': 30,
        'status': 'processing',
        'message': f'PDF类型检测完成: {reason}',
        'filename': filename
      }
      
      if task_id:
        await update_file_status(task_id, file_id, file_index, progress_info)
      else:
        await loop.run_in_executor(
          thread_executor,
          redis_client.setex,
          progress_key,
          1800,
          json.dumps(progress_info)
        )
      
      if is_standard:
        logger.info(f"处理标准PDF文件: {filename}")
        
        # 进度回调
        async def progress_cb(current, total):
          progress = min(30 + int(60 * current / max(total, 1)), 90)
          progress_info = {
            'progress': progress,
            'status': 'processing',
            'message': f'正在处理PDF页面: {current}/{total}',
            'filename': filename
          }
          
          if task_id:
            await update_file_status(task_id, file_id, file_index, progress_info)
          else:
            await loop.run_in_executor(
              thread_executor,
              redis_client.setex,
              progress_key,
              1800,
              json.dumps(progress_info)
            )
        
        result = await pdf_to_text_with_progress(
          file_location, max_file_chat_size, progress_cb, filename, username
        )
        
        # 解包返回值
        if len(result) == 4:
          text, processed_pages, total_pages, is_partial = result
        else:  # 兼容旧版函数
          text, processed_pages, total_pages = result
          is_partial = processed_pages < total_pages
      else:
        # 图片类PDF，使用OCR处理
        logger.info(f"检测到图片类PDF，启动OCR处理: {filename}")
        
        # 进度回调
        async def progress_cb(current, total):
          progress = min(30 + int(60 * current / max(total, 1)), 90)
          progress_info = {
            'progress': progress,
            'status': 'processing',
            'message': f'OCR处理中: {current}/{total}',
            'filename': filename
          }
          
          if task_id:
            await update_file_status(task_id, file_id, file_index, progress_info)
          else:
            await loop.run_in_executor(
              thread_executor,
              redis_client.setex,
              progress_key,
              1800,
              json.dumps(progress_info)
            )
        
        text, processed_pages, total_pages, ocr_file_id = await ocr_processor_with_progress(
          file_location, max_file_chat_size, progress_cb, filename, username, request_headers
        )
        is_partial = processed_pages < total_pages
        logger.info(f"OCR处理完成，提取文本长度: {len(text)}")
    
    elif file_type in ['docx', 'doc']:
      logger.info(f"处理Word文档: {filename}")
      
      # 进度回调
      async def progress_cb(current, total):
        progress = min(30 + int(60 * current / max(total, 1)), 90)
        progress_info = {
          'progress': progress,
          'status': 'processing',
          'message': f'正在处理Word文档: {current}/{total}',
          'filename': filename
        }
        
        if task_id:
          await update_file_status(task_id, file_id, file_index, progress_info)
        else:
          await loop.run_in_executor(
            thread_executor,
            redis_client.setex,
            progress_key,
            1800,
            json.dumps(progress_info)
          )
      
      result = await word_to_text_with_progress(
        file_location, max_file_chat_size, progress_cb
      )
      
      # 解包返回值
      if len(result) == 4:
        text, processed_pages, total_pages, is_partial = result
      else:
        text, processed_pages, total_pages = result
        is_partial = processed_pages < total_pages
    
    elif file_type in ['jpg', 'png']:
      logger.info(f"处理图片文件: {filename}")
      
      # 进度回调
      async def progress_cb(current, total):
        progress = min(30 + int(60 * current / max(total, 1)), 90)
        progress_info = {
          'progress': progress,
          'status': 'processing',
          'message': f'OCR处理图片中: {current}/{total}',
          'filename': filename
        }
        
        if task_id:
          await update_file_status(task_id, file_id, file_index, progress_info)
        else:
          await loop.run_in_executor(
            thread_executor,
            redis_client.setex,
            progress_key,
            1800,
            json.dumps(progress_info)
          )
      
      # 直接使用OCR处理图片文件
      text, processed_pages, total_pages, ocr_file_id = await process_image_with_ocr(
        file_location, max_file_chat_size, progress_cb, filename, username, request_headers
      )
      logger.info(f"图片OCR处理完成，提取文本长度: {len(text)}")
    
    # 检查文本长度是否超过限制，如果超过则截断
    if len(text) > max_file_chat_size:
      logger.warning(f"文件内容超过限制，将进行截断。原始长度: {len(text)}")
      truncated_text = text[:max_file_chat_size - 50]  # 留出50字符空间用于添加说明
      truncated_text += f"\n\n[注意: 由于文本内容过大，系统只保留了约{max_file_chat_size}个字符]"
      text = truncated_text
      is_partial = True
    
    # 更新进度到95%
    progress_info = {
      'progress': 95,
      'status': 'processing',
      'message': '文件内容提取完成，正在保存',
      'filename': filename
    }
    
    if task_id:
      await update_file_status(task_id, file_id, file_index, progress_info)
    else:
      await loop.run_in_executor(
        thread_executor,
        redis_client.setex,
        progress_key,
        1800,
        json.dumps(progress_info)
      )
    
    # 存储结果到Redis
    redis_key = f"{redis_table}:file:{file_id}"
    logger.info(f"准备将文件内容存储到Redis, 键: {redis_key}, 内容长度: {len(text)}, 过期时间: {history_exp_time}秒")
    
    try:
      # 记录文本实际字符数
      actual_chars = len(text)
      logger.info(f"文件 {file_id} 实际字符数: {actual_chars}")
      
      # 保存到Redis
      logger.info(f"开始Redis setex操作: {redis_key}")
      await loop.run_in_executor(
        thread_executor,
        redis_client.setex,
        redis_key,
        history_exp_time,
        text
      )
      logger.info(f"Redis存储成功: {redis_key}")
      
      # 如果提供了check_id，更新该section的文件列表和总字符数
      if check_id and text:
        logger.info(f"更新section {check_id}的文件列表和字符统计")
        # 获取section中的文件列表
        section_key = f"{redis_table}:section:{check_id}"
        section_file_ids = redis_client.get(section_key)
        
        file_ids = []
        if section_file_ids:
          file_ids = json.loads(section_file_ids)
        
        # 添加当前文件ID到列表中
        if file_id not in file_ids:
          file_ids.append(file_id)
        
        # 更新section文件列表
        await loop.run_in_executor(
          thread_executor,
          redis_client.setex,
          section_key,
          history_exp_time,
          json.dumps(file_ids)
        )
        
        # 计算该section下所有文件的字符总数
        total_chars = 0
        for fid in file_ids:
          file_content = get_file_text(fid)
          if file_content:
            file_chars = len(file_content)
            total_chars += file_chars
            logger.info(f"文件 {fid} 字符数: {file_chars}")
        
        logger.info(f"Section {check_id} 所有文件总字符数: {total_chars}")
        
        # 将字符数结果存储到Redis
        chars_key = f"{redis_table}:section_chars:{check_id}"
        chars_info = {
          'total_chars': total_chars,
          'file_count': len(file_ids),
          'updated_time': time.time()
        }
        
        if total_chars > 50000:
          chars_info['warning'] = True
          chars_info['message'] = f'当前分组下的所有文件内容总字符数超过模型推荐限制(50000)'
          logger.info(f"Section {check_id} 字符数超限: {total_chars} > 50000")
        
        await loop.run_in_executor(
          thread_executor,
          redis_client.setex,
          chars_key,
          history_exp_time,
          json.dumps(chars_info)
        )
      
      # 修改完成状态消息以反映部分处理情况
      completion_message = '文件处理完成'
      if is_partial:
        completion_message = f'文件部分处理完成(内容过大，仅处理了部分页)'
        logger.warning(f"文件 {file_id} ({filename}) 因内容过大而部分处理: {processed_pages}/{total_pages}")
      
      final_status = {
        'progress': 100,
        'status': 'completed',
        'message': completion_message,
        'processed_pages': processed_pages,
        'total_pages': total_pages,
        'exp_time': int(time.time()) + history_exp_time,
        'file_id': file_id,
        'filename': filename,
        'is_partial': is_partial,  # 添加部分处理标记
        'chars_count': actual_chars,  # 添加实际字符数
        'may_truncate': will_truncate  # 保留可能截断的标记
      }
      
      # 保留估算字符数（针对非PDF文件）
      if not is_pdf and 'estimated_chars' in progress_info:
        final_status['estimated_chars'] = estimated_chars
      
      # 添加OCR文件ID到返回结果中
      if ocr_file_id:
        final_status['ocr_file_id'] = ocr_file_id
        logger.info(f"添加OCR文件ID到结果: {ocr_file_id}")
      
      # 添加check_id到返回结果中
      if check_id:
        final_status['check_id'] = check_id
        
        # 如果section的字符数超过限制，添加警告信息而非错误
        chars_key = f"{redis_table}:section_chars:{check_id}"
        chars_info_json = redis_client.get(chars_key)
        if chars_info_json:
          chars_info = json.loads(chars_info_json)
          if chars_info.get('warning'):
            final_status['chars_warning'] = chars_info.get('message')
            # 添加警告对象
            final_status['warning'] = {
              'section_warning': True,
              'total_chars': chars_info.get('total_chars', 0),
              'file_count': chars_info.get('file_count', 0),
              'limit': 50000
            }
      
      # 更新最终进度和状态
      if task_id:
        await update_file_status(task_id, file_id, file_index, final_status)
      else:
        await loop.run_in_executor(
          thread_executor,
          redis_client.setex,
          progress_key,
          1800,
          json.dumps(final_status)
        )
      
      try:
        await loop.run_in_executor(thread_executor, os.remove, file_location)
        logger.info(f"临时文件已清理: {file_location}")
      except Exception as cleanup_error:
        logger.error(f"清理临时文件失败: {cleanup_error}")
    
    except Exception as e:
      logger.error(f"Redis存储文件内容失败: {e}", exc_info=True)
      # 更新错误状态
      error_info = {
        'progress': 100,
        'status': 'error',
        'message': f'文件内容缓存失败: {str(e)}，请稍后重试',
        'filename': filename
      }
      
      if task_id:
        await update_file_status(task_id, file_id, file_index, error_info)
      else:
        await loop.run_in_executor(
          thread_executor,
          redis_client.setex,
          progress_key,
          1800,
          json.dumps(error_info)
        )
      
      try:
        await loop.run_in_executor(thread_executor, os.remove, file_location)
      except Exception as cleanup_error:
        logger.error(f"清理临时文件失败: {cleanup_error}")
  
  except Exception as e:
    logger.error(f"异步文件处理失败: {e}", exc_info=True)
    # 更新错误状态
    error_info = {
      'progress': 100,
      'status': 'error',
      'message': f'处理失败: {str(e)}',
      'filename': filename
    }
    
    if task_id:
      await update_file_status(task_id, file_id, file_index, error_info)
    else:
      await loop.run_in_executor(
        thread_executor,
        redis_client.setex,
        progress_key,
        1800,
        json.dumps(error_info)
      )
    
    try:
      await loop.run_in_executor(thread_executor, os.remove, file_location)
    except Exception as cleanup_error:
      logger.error(f"清理临时文件失败: {cleanup_error}")


async def process_image_with_ocr(image_path, max_size, progress_callback, original_filename="", username="", request_headers=None):
  """
  使用OCR处理单张图片文件

  Args:
      image_path: 图片文件路径
      max_size: 最大文本大小
      progress_callback: 进度回调函数

  Returns:
      tuple: (text, processed_pages, total_pages, ocr_file_id)
  """
  try:
    loop = asyncio.get_running_loop()
    
    # 创建临时目录
    temp_dir = await loop.run_in_executor(
      thread_executor,
      tempfile.mkdtemp,
      "ocr_temp_",
      TEMP_BASE_DIR  # 指定父目录
    )
    logger.info(f"创建临时目录用于图片OCR处理: {temp_dir}")
    
    try:
      # 获取OCR配置
      ocr_config = get_ocr_config()
      client = ocr_processor.APIClient(ocr_config["base_url"], ocr_config["auth_token"])
      track_id = ocr_config["track_id"]
      params = ocr_config["params"]
      
      # 更新进度到40%
      await progress_callback(4, 10)
      
      # 上传图片到OCR服务
      upload_result = await loop.run_in_executor(
        thread_executor,
        client.upload_image,
        track_id,
        image_path,
        '',
        params
      )
      
      # 更新进度到60%
      await progress_callback(6, 10)
      logger.info(f"图片上传到OCR服务成功: {image_path}")
      
      # 创建Word文档
      output_docx_path = os.path.join(temp_dir, "ocr_result.docx")
      
      # 解析OCR结果并创建Word文档
      parse_result = await loop.run_in_executor(
        thread_executor,
        ocr_processor.parse_and_create_word_document,
        [upload_result],
        output_docx_path
      )
      
      # 更新进度到80%
      await progress_callback(8, 10)
      
      if parse_result["code"] != 200:
        logger.error(f"OCR创建Word文档失败: {parse_result['message']}")
        return "OCR处理失败，无法创建文档", 0, 1, None
      
      # 从Word文档中提取文本
      text = await loop.run_in_executor(
        thread_executor,
        ocr_processor.extract_text_from_docx,
        output_docx_path
      )
      
      # 上传OCR生成的Word文档到文件服务
      ocr_file_id = None
      try:
        with open(output_docx_path, "rb") as f:
          file_bytes = f.read()
        
        # 计算文件MD5
        file_md5 = file_util.md5(os.path.basename(output_docx_path) + str(time.time()))
        
        original_name = os.path.splitext(original_filename)[0] if original_filename else "image"
        file_name = f"{original_name}_OCR.docx"
        
        upload_headers = {}
        
        if username:
          upload_headers["X-Username"] = username
        
        if request_headers and 'x-api-key' in request_headers:
          upload_headers["X-API-Key"] = request_headers['x-api-key']
        
        logger.info(f"准备上传OCR文档，请求头: {upload_headers}")
        
        try:
          ocr_file_id = await file_util.upload_file(file_bytes, file_md5, file_name, "ocr_results", upload_headers)
          logger.info(f"OCR结果文档上传成功，文件ID: {ocr_file_id}")
        except Exception as e:
          logger.warning(f"使用指定bucket上传失败: {e}")
          try:
            ocr_file_id = await file_util.upload_file(file_bytes, file_md5, file_name, None, upload_headers)
            logger.info(f"OCR结果文档上传成功（使用默认路径），文件ID: {ocr_file_id}")
          except Exception as default_e:
            logger.error(f"使用默认路径上传也失败: {default_e}")
      except Exception as e:
        logger.error(f"上传OCR结果文档失败: {e}")
      
      # 更新进度到90%
      await progress_callback(9, 10)
      
      logger.info(f"图片OCR处理完成，提取文本长度: {len(text)}")
      
      return text, 1, 1, ocr_file_id  # 图片视为1页，加上ocr_file_id
    
    finally:
      # 清理临时目录
      try:
        await loop.run_in_executor(thread_executor, shutil.rmtree, temp_dir)
        logger.info(f"OCR临时目录清理完成: {temp_dir}")
      except Exception as e:
        logger.error(f"清理临时目录失败: {e}")
  
  except Exception as e:
    logger.error(f"图片OCR处理失败: {e}", exc_info=True)
    return f"图片OCR处理失败: {str(e)}", 0, 1, None
  
def get_text_and_replace_spaces(page):
  """
  从PDF页面获取文本并替换空格

  Args:
      page: PDF页面对象

  Returns:
      str: 处理后的文本
  """
  return page.get_text().replace(' ', '')


async def pdf_to_text_with_progress(file_path, max_size, progress_callback, filename="", username=""):
  """带进度回调的PDF文本提取函数"""
  try:
    # 导入必要的模块
    from process_pool_manager import process_pool_manager
    
    loop = asyncio.get_running_loop()
    kind = await loop.run_in_executor(thread_executor, filetype.guess, file_path)
    
    if not kind or kind.mime != 'application/pdf':
      mime_type = kind.mime if kind else "未知"
      logger.error(f"文件类型错误: {mime_type}，应为PDF")
      return f"文件类型错误: {mime_type}，应为PDF", 0, 0, False
    
    def get_pdf_pages(fp):
      with fitz.open(fp) as doc:
        return len(doc)
    
    total_pages = await loop.run_in_executor(thread_executor, get_pdf_pages, file_path)
    
    text = ""
    processed_pages = 0
    is_partial = False
    
    # 使用改进的进程池处理，增加重试机制
    max_retries = 3
    last_exception = None
    
    for attempt in range(max_retries):
      try:
        logger.info(f"PDF处理尝试 {attempt + 1}/{max_retries}: {filename}")
        
        future = process_pool_manager.submit_with_retry(
          extract_pdf_text_safe,
          file_path,
          max_size,
          max_retries=1
        )
        
        result = await asyncio.wait_for(
          loop.run_in_executor(None, future.result),
          timeout=600  # 10分钟超时
        )
        
        text, processed_pages, total_pages, is_partial = result
        logger.info(f"PDF处理成功: {processed_pages}/{total_pages}页")
        break
      
      except (concurrent.futures.process.BrokenProcessPool,
              RuntimeError,
              OSError) as e:
        last_exception = e
        logger.warning(f"PDF进程池处理失败(尝试 {attempt + 1}/{max_retries}): {e}")
        
        if attempt < max_retries - 1:
          await asyncio.sleep(2)
          continue
        else:
          # 使用线程池作为备用方案
          logger.warning("进程池处理失败，使用线程池备用方案")
          try:
            result = await pdf_fallback_with_threads(file_path, max_size, progress_callback, total_pages, loop)
            text, processed_pages, total_pages, is_partial = result
            logger.info("线程池备用方案执行成功")
            break
          except Exception as thread_e:
            logger.error(f"线程池备用方案也失败: {thread_e}")
            return f"处理PDF文件失败: {str(thread_e)}", 0, 0, False
      
      except asyncio.TimeoutError:
        logger.error(f"PDF处理超时(尝试 {attempt + 1}/{max_retries})")
        last_exception = TimeoutError("处理超时")
        if attempt < max_retries - 1:
          await asyncio.sleep(2)
          continue
        else:
          return f"处理PDF文件超时", 0, 0, False
      
      except Exception as e:
        last_exception = e
        logger.error(f"PDF处理异常(尝试 {attempt + 1}/{max_retries}): {e}")
        if attempt < max_retries - 1:
          await asyncio.sleep(2)
          continue
        else:
          return f"处理PDF文件失败: {str(e)}", 0, 0, False
    
    # 处理部分内容的说明
    if is_partial or processed_pages < total_pages:
      is_partial = True
      processed_percent = (processed_pages / total_pages) * 100
      truncation_note = f"\n\n[注意: 由于文档过大，系统只处理了前 {processed_pages} 页（共 {total_pages} 页），约占总内容的 {processed_percent:.1f}%]"
      
      if len(text) + len(truncation_note) <= max_size:
        text += truncation_note
      else:
        max_text_len = max_size - len(truncation_note)
        text = text[:max_text_len] + truncation_note
    
    logger.info(f"PDF处理完成: 共{total_pages}页, 已处理{processed_pages}页, 是否部分处理: {is_partial}")
    
    return text, processed_pages, total_pages, is_partial
  
  except Exception as e:
    logger.error(f"PDF文本提取异常: {e}", exc_info=True)
    return f"处理PDF文件失败: {str(e)}", 0, 0, False
  
def extract_pdf_text_safe(file_path, max_size):
  """PDF文本提取函数"""
  try:
    import fitz
    
    with fitz.open(file_path) as doc:
      total_pages = len(doc)
      text = ""
      processed_pages = 0
      
      for idx in range(total_pages):
        try:
          page = doc.load_page(idx)
          now_text = page.get_text().replace(' ', '')
          
          if len(text) + len(now_text) > max_size:
            break
          
          text += f'第{idx + 1}页\n{now_text}\n'
          processed_pages += 1
        except Exception as e:
          logger.error(f"处理PDF第{idx + 1}页时出错: {e}")
      
      is_partial = processed_pages < total_pages
      return text, processed_pages, total_pages, is_partial
  
  except Exception as e:
    logger.error(f"PDF处理异常: {e}")
    return f"PDF处理失败: {str(e)}", 0, 0, False


async def pdf_fallback_with_threads(file_path, max_size, progress_callback, total_pages, loop):
  """PDF处理的线程池备用方案"""
  try:
    text = ""
    processed_pages = 0
    is_partial = False
    
    doc = await loop.run_in_executor(thread_executor, fitz.open, file_path)
    
    try:
      for idx in range(total_pages):
        # 更新进度
        await progress_callback(idx + 1, total_pages)
        
        page = await loop.run_in_executor(thread_executor, doc.load_page, idx)
        now_text = await loop.run_in_executor(
          thread_executor,
          lambda p: p.get_text().replace(' ', ''),
          page
        )
        
        if len(text) + len(now_text) > max_size:
          logger.warning(f"PDF内容超过最大限制，在第{idx + 1}页停止处理")
          is_partial = True
          break
        
        text += f'第{idx + 1}页\n{now_text}'
        processed_pages += 1
        
        # 每处理5页休息一下，避免阻塞
        if idx % 5 == 0:
          await asyncio.sleep(0.01)
      
      return text, processed_pages, total_pages, is_partial
    
    finally:
      await loop.run_in_executor(thread_executor, doc.close)
  
  except Exception as e:
    logger.error(f"PDF线程池备用方案失败: {e}")
    raise

async def word_to_text_with_progress(file_path, max_size, progress_callback):
  """更新后的带进度回调的Word文档文本提取函数"""
  try:
    loop = asyncio.get_running_loop()
    
    # 检查文件类型
    kind = await loop.run_in_executor(thread_executor, filetype.guess, file_path)
    
    if not kind:
      ext = os.path.splitext(file_path)[1].lower()
      if ext not in ['.docx', '.doc']:
        logger.error(f"文件类型无法识别，请确保是Word文档: {file_path}")
        return f"文件类型错误：无法识别的文件类型，应为Word文档", 0, 0, False
    elif kind.mime not in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                           'application/msword']:
      logger.error(f"文件类型错误: {kind.mime}，应为Word文档")
      return f"文件类型错误: {kind.mime}，应为Word文档", 0, 0, False
    
    # 估算总段落数
    total_paragraphs = 0
    if file_path.endswith('.docx'):
      try:
        doc = await loop.run_in_executor(thread_executor, docx.Document, file_path)
        total_paragraphs = len(doc.paragraphs)
      except Exception as e:
        logger.warning(f"无法获取Word文档段落数: {e}")
        total_paragraphs = 100
    else:
      file_size = os.path.getsize(file_path)
      total_paragraphs = max(1, int(file_size / (30 * 1024)))
    
    await progress_callback(0, total_paragraphs)
    
    # 使用改进的进程池执行器，增加重试机制
    max_retries = 3
    last_exception = None
    
    for attempt in range(max_retries):
      try:
        future = process_pool_manager.submit_with_retry(
          word_to_text_worker_V2,
          file_path,
          max_size,
          max_retries=1  # 进程池内部重试1次
        )
        
        # 等待结果，设置超时
        result = await asyncio.wait_for(
          loop.run_in_executor(None, future.result),
          timeout=300  # 5分钟超时
        )
        
        # 成功获取结果，跳出重试循环
        break
      
      except (concurrent.futures.process.BrokenProcessPool,
              RuntimeError,
              OSError) as e:
        last_exception = e
        logger.warning(f"Word文档处理失败(尝试 {attempt + 1}/{max_retries}): {e}")
        
        if attempt < max_retries - 1:
          # 等待一段时间后重试
          await asyncio.sleep(1)
          continue
        else:
          # 最后一次尝试失败，返回错误
          logger.error("所有重试都失败了")
          return f"处理Word文档失败: {str(last_exception)}", 0, 0, False
      
      except asyncio.TimeoutError:
        logger.error(f"Word文档处理超时(尝试 {attempt + 1}/{max_retries})")
        last_exception = TimeoutError("处理超时")
        if attempt < max_retries - 1:
          await asyncio.sleep(1)
          continue
        else:
          return f"处理Word文档超时", 0, 0, False
      
      except Exception as e:
        last_exception = e
        logger.error(f"Word文档处理异常(尝试 {attempt + 1}/{max_retries}): {e}")
        if attempt < max_retries - 1:
          await asyncio.sleep(1)
          continue
        else:
          return f"处理Word文档失败: {str(e)}", 0, 0, False
    
    # 解包返回值
    if len(result) == 4:
      text, processed_paragraphs, total, is_partial = result
    else:
      text, processed_paragraphs, total = result
      is_partial = processed_paragraphs < total
    
    # 最终进度更新
    await progress_callback(processed_paragraphs, total)
    
    if is_partial:
      logger.warning(f"文档内容过大，仅处理了 {processed_paragraphs}/{total} 段落")
    
    return text, processed_paragraphs, total, is_partial
  
  except Exception as e:
    logger.error(f"Word文档处理异常: {e}", exc_info=True)
    return f"处理Word文档失败: {str(e)}", 0, 0, False
  
async def ocr_processor_with_progress(file_path, max_size, progress_callback, original_filename="", username="",
                                      request_headers=None):
  """带进度回调的OCR处理函数"""
  try:
    from process_pool_manager import process_pool_manager
    
    loop = asyncio.get_running_loop()
    
    # 获取PDF页数 - 使用线程池
    total_pages = await loop.run_in_executor(
      thread_executor,
      lambda fp: len(fitz.open(fp)),
      file_path
    )
    
    # 初始进度更新
    await progress_callback(0, total_pages)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix="ocr_temp_", dir=TEMP_BASE_DIR)
    
    try:
      # 获取OCR配置
      ocr_config = get_ocr_config()
      client = ocr_processor.APIClient(ocr_config["base_url"], ocr_config["auth_token"])
      track_id = ocr_config["track_id"]
      params = ocr_config["params"]
      
      all_results = []
      processed_pages = 0
      
      logger.info(f"开始处理PDF文件的OCR: {file_path}, 总页数: {total_pages}")
      
      # 第一阶段：提取图像（使用进程池，有重试机制）
      image_paths = await extract_images_with_retry(file_path, total_pages, temp_dir, progress_callback, loop)
      
      if not image_paths:
        logger.error("所有图像提取都失败了")
        return "图像提取失败", 0, total_pages, None
      
      logger.info(f"图像提取完成，成功提取: {len(image_paths)}/{total_pages}页")
      
      # 第二阶段：OCR识别（使用线程池处理网络请求）
      await progress_callback(int(total_pages * 0.3), total_pages)
      
      sorted_images = sorted(image_paths, key=lambda x: int(os.path.basename(x).split('_')[1].split('.')[0]))
      
      # 分批处理OCR，避免过多并发
      batch_size = min(5, len(sorted_images))  # 每批最多5个
      
      for i in range(0, len(sorted_images), batch_size):
        batch = sorted_images[i:i + batch_size]
        
        # 使用线程池处理OCR上传
        upload_tasks = []
        for image_path in batch:
          task = loop.run_in_executor(
            thread_executor,
            client.upload_image,
            track_id,
            image_path,
            '',
            params
          )
          upload_tasks.append(task)
        
        # 等待当前批次完成
        batch_results = await asyncio.gather(*upload_tasks, return_exceptions=True)
        
        # 处理结果并更新进度
        for result in batch_results:
          if not isinstance(result, Exception):
            all_results.append(result)
            processed_pages += 1
          else:
            logger.error(f"OCR处理失败: {result}")
        
        # 更新进度
        progress_percentage = 0.3 + (processed_pages / max(len(sorted_images), 1)) * 0.6
        await progress_callback(int(total_pages * progress_percentage), total_pages)
        
        # 批次间短暂休息
        await asyncio.sleep(0.1)
      
      logger.info(f"OCR图像上传和识别完成: {processed_pages}/{len(sorted_images)}页")
      
      # 第三阶段：创建Word文档
      output_docx_path = os.path.join(temp_dir, "ocr_result.docx")
      
      # 更新进度
      await progress_callback(int(total_pages * 0.9), total_pages)
      
      # 解析OCR结果并创建Word文档 - 使用线程池
      parse_result = await loop.run_in_executor(
        thread_executor,
        ocr_processor.parse_and_create_word_document,
        all_results,
        output_docx_path
      )
      
      if parse_result["code"] != 200:
        logger.error(f"OCR创建Word文档失败: {parse_result['message']}")
        return "OCR处理失败，无法创建文档", 0, total_pages, None
      
      # 从Word文档中提取文本 - 使用线程池
      text = await loop.run_in_executor(
        thread_executor,
        ocr_processor.extract_text_from_docx,
        output_docx_path
      )
      
      logger.info(f"OCR处理完成，提取文本长度: {len(text)}")
      
      # 第四阶段：上传OCR生成的Word文档到文件服务
      ocr_file_id = None
      try:
        ocr_file_id = await upload_ocr_document(output_docx_path, original_filename, username, request_headers, loop)
      except Exception as e:
        logger.error(f"上传OCR结果文档失败: {e}")
      
      # 最终进度更新
      await progress_callback(total_pages, total_pages)
      
      return text, processed_pages, total_pages, ocr_file_id
    
    finally:
      # 清理临时文件 - 异步执行
      try:
        await loop.run_in_executor(thread_executor, shutil.rmtree, temp_dir)
        logger.info(f"OCR临时目录清理完成: {temp_dir}")
      except Exception as e:
        logger.error(f"清理临时目录失败: {e}")
  
  except Exception as e:
    logger.error(f"OCR处理PDF失败: {e}", exc_info=True)
    return f"OCR处理失败: {str(e)}", 0, 0, None


async def extract_images_with_retry(file_path, total_pages, temp_dir, progress_callback, loop):
  """带重试机制的图像提取"""
  from process_pool_manager import process_pool_manager
  
  max_retries = 2
  
  for attempt in range(max_retries):
    try:
      logger.info(f"图像提取尝试 {attempt + 1}/{max_retries}")
      
      # 使用进程池批量提取图像
      extract_tasks = []
      for page_num in range(total_pages):
        output_path = os.path.join(temp_dir, f"page_{page_num}.png")
        
        future = process_pool_manager.submit_with_retry(
          ocr_processor.extract_image_from_page_worker,
          file_path,
          page_num,
          output_path,
          max_retries=1
        )
        extract_tasks.append((page_num, future))
      
      # 等待所有图像提取完成，设置超时
      image_paths = []
      for page_num, future in extract_tasks:
        try:
          image_path = await asyncio.wait_for(
            loop.run_in_executor(None, future.result),
            timeout=30  # 每页30秒超时
          )
          if image_path:
            image_paths.append(image_path)
          
          # 更新进度
          progress = int(total_pages * 0.3 * (page_num + 1) / total_pages)
          await progress_callback(progress, total_pages)
        
        except Exception as e:
          logger.warning(f"第{page_num + 1}页图像提取失败: {e}")
      
      if image_paths:
        logger.info(f"进程池图像提取成功: {len(image_paths)}/{total_pages}页")
        return image_paths
      else:
        raise Exception("所有页面的图像提取都失败了")
    
    except Exception as e:
      logger.warning(f"进程池图像提取失败(尝试 {attempt + 1}/{max_retries}): {e}")
      
      if attempt < max_retries - 1:
        await asyncio.sleep(2)
        continue
      else:
        # 最后尝试使用线程池作为备用方案
        logger.warning("使用线程池作为图像提取的备用方案")
        try:
          return await extract_images_with_threads(file_path, total_pages, temp_dir, progress_callback, loop)
        except Exception as thread_e:
          logger.error(f"线程池备用方案也失败: {thread_e}")
          return []


async def extract_images_with_threads(file_path, total_pages, temp_dir, progress_callback, loop):
  """使用线程池的图像提取备用方案"""
  try:
    image_paths = []
    
    for page_num in range(total_pages):
      try:
        output_path = os.path.join(temp_dir, f"page_{page_num}.png")
        
        # 使用线程池提取单页图像
        image_path = await loop.run_in_executor(
          thread_executor,
          ocr_processor.extract_image_from_page_worker,
          file_path,
          page_num,
          output_path
        )
        
        if image_path:
          image_paths.append(image_path)
        
        # 更新进度
        progress = int(total_pages * 0.3 * (page_num + 1) / total_pages)
        await progress_callback(progress, total_pages)
        
        # 避免阻塞
        if page_num % 2 == 0:
          await asyncio.sleep(0.01)
      
      except Exception as e:
        logger.error(f"线程池提取第{page_num + 1}页图像失败: {e}")
    
    return image_paths
  
  except Exception as e:
    logger.error(f"线程池图像提取失败: {e}")
    return []


async def upload_ocr_document(output_docx_path, original_filename, username, request_headers, loop):
  """上传OCR生成的Word文档"""
  try:
    # 读取文件内容
    with open(output_docx_path, "rb") as f:
      file_bytes = f.read()
    
    # 计算文件MD5
    file_md5 = file_util.md5(os.path.basename(output_docx_path) + str(time.time()))
    
    original_name = os.path.splitext(original_filename)[0] if original_filename else "document"
    file_name = f"{original_name}_OCR.docx"
    
    # 构建上传请求头
    upload_headers = {}
    if username:
      upload_headers["X-Username"] = username
    
    if request_headers and 'x-api-key' in request_headers:
      upload_headers["X-API-Key"] = request_headers['x-api-key']
    
    logger.info(f"准备上传OCR文档，请求头: {upload_headers}")
    
    # 上传文件
    bucket_name = "ocr_results"
    ocr_file_id = await file_util.upload_file(file_bytes, file_md5, file_name, bucket_name, upload_headers)
    
    logger.info(f"OCR结果文档上传成功，文件ID: {ocr_file_id}")
    return ocr_file_id
  
  except Exception as e:
    logger.error(f"上传OCR结果文档失败: {e}")
    return None


@router.get("/jiliang/file_progress/{task_id}")
async def get_file_progress(request: Request, task_id: str):
  """查询文件处理进度"""
  try:
    # 获取请求头
    header = dict(request.headers)
    logger.debug(f"file_progress: {header}")
    apikey = ''
    userid = ''
    if 'x-api-key' in header:
      apikey = header['x-api-key']
    if 'x-username' in header:
      userid = header['x-username']
    
    # 验证必要的头信息
    if not apikey or not userid:
      return R.error('401', '缺少必要的认证信息')
    
    # 检查是否为批次ID
    batch_progress_key = f"{redis_table}:batch_progress:{task_id}"
    batch_info = redis_client.get(batch_progress_key)
    
    if batch_info:
      batch_data = json.loads(batch_info)
      
      result = {
        'task_id': task_id,
        'status': batch_data.get('status', 'processing'),
        'progress': batch_data.get('progress', 0),
        'message': batch_data.get('message', '正在处理'),
        'total_files': batch_data.get('total_files', 0),
        'processed_files': batch_data.get('processed_files', 0),
        'file_details': batch_data.get('file_details', []),
      }
      
      file_ids = [file_detail.get('file_id') for file_detail in batch_data.get('file_details', [])]
      result['file_ids'] = file_ids
      
      if batch_data.get('file_details') and len(batch_data.get('file_details')) > 0:
        first_file = batch_data.get('file_details')[0]

        for key in ['file_id', 'filename', 'ocr_file_id', 'processed_pages', 'total_pages',
                    'is_partial', 'chars_count', 'warning', 'check_id',
                    'may_truncate', 'estimated_chars', 'is_pdf', 'exp_time']:
          if key in first_file:
            result[key] = first_file[key]

        if len(batch_data.get('file_details')) == 1 and 'message' in first_file:
          result['message'] = first_file['message']


      if 'exp_time' not in result:
        result['exp_time'] = int(time.time()) + history_exp_time

      return R.ok(result)
    else:
      # 检查是否为单个文件ID
      progress_key = f"{redis_table}:progress:{task_id}"
      progress_data = redis_client.get(progress_key)
      
      if not progress_data:
        return R.error('404', '找不到文件处理记录或处理已完成')
      
      progress_info = json.loads(progress_data)
      
      file_id = progress_info.get('file_id', task_id)
      
      if progress_info.get('status') == 'completed':
        result = {
          'file_id': file_id,
          'status': 'completed',
          'progress': 100,
          'message': progress_info.get('message', '已完成'),
          'processed_pages': progress_info.get('processed_pages', 0),
          'total_pages': progress_info.get('total_pages', 0),
          'exp_time': progress_info.get('exp_time', int(time.time()) + history_exp_time)
        }
        
        for key in ['filename', 'ocr_file_id', 'task_id', 'check_id', 'is_partial',
                    'chars_count', 'may_truncate', 'is_pdf', 'estimated_chars']:
          if key in progress_info:
            result[key] = progress_info[key]
        
        # 处理warning对象
        if 'may_truncate' in progress_info and progress_info['may_truncate']:
          warning_info = {
            'may_truncate': True,
            'message': f"文件内容较大，可能需要截断处理"
          }
          
          if 'estimated_chars' in progress_info:
            warning_info['estimated_chars'] = progress_info['estimated_chars']
            warning_info['message'] = f"文件内容较大（估算字符数：{progress_info['estimated_chars']}），可能需要截断处理"
            warning_info['limit'] = 50000
          
          # 添加实际字符数
          if 'chars_count' in progress_info:
            warning_info['actual_chars'] = progress_info['chars_count']
          
          # 添加是否为PDF标志
          if 'is_pdf' in progress_info:
            warning_info['is_pdf'] = progress_info['is_pdf']
          
          result['warning'] = warning_info
        
        # 添加分区警告信息
        if 'chars_warning' in progress_info:
          if 'warning' not in result:
            result['warning'] = {}
          result['warning']['section_warning'] = progress_info.get('chars_warning')
        
        result['file_details'] = [{
          'file_id': file_id,
          'file_index': 0,
          'filename': progress_info.get('filename', ''),
          'status': 'completed',
          'progress': 100,
          'message': progress_info.get('message', '已完成')
        }]

        return R.ok(result)
      
      if progress_info.get('status') == 'error':
        return R.error('500', progress_info.get('message', '处理失败'))
      
      result = {
        'file_id': file_id,
        'status': 'processing',
        'progress': progress_info.get('progress', 0),
        'message': progress_info.get('message', '正在处理'),
        'exp_time': int(time.time()) + history_exp_time
      }
      
      for key in ['filename', 'task_id', 'check_id', 'may_truncate',
                  'is_pdf', 'estimated_chars']:
        if key in progress_info:
          result[key] = progress_info[key]
      
      # 添加处理中的文件警告信息
      if 'may_truncate' in progress_info and progress_info['may_truncate']:
        warning_info = {
          'may_truncate': True,
          'message': f"文件内容较大，可能需要截断处理"
        }
        
        # 添加估算字符数到警告
        if 'estimated_chars' in progress_info:
          warning_info['estimated_chars'] = progress_info['estimated_chars']
          warning_info['message'] = f"文件内容较大（解析后字符数：{progress_info['estimated_chars']}），可能需要截断处理"
          warning_info['limit'] = 50000
        
        # 添加是否为PDF标志
        if 'is_pdf' in progress_info:
          warning_info['is_pdf'] = progress_info['is_pdf']
        
        result['warning'] = warning_info
      
      result['file_details'] = [{
        'file_id': file_id,
        'file_index': 0,
        'filename': progress_info.get('filename', ''),
        'status': 'processing',
        'progress': progress_info.get('progress', 0),
        'message': progress_info.get('message', '正在处理')
      }]

      return R.ok(result)
  
  except Exception as e:
    logger.error(f"查询文件处理进度失败: {e}", exc_info=True)
    return R.error('500', f'查询处理进度失败: {str(e)}')


def get_file_text(file_id):
  """
  从Redis获取文件文本内容

  Args:
      file_id: 文件ID

  Returns:
      str: 文件文本内容，如果获取失败返回None
  """
  if not file_id:
    logger.info("file_id为空")
    return None
  
  try:
    key = f"{redis_table}:file:{file_id}"
    content = redis_client.get(key)
    
    if content is None:
      logger.info(f"Redis中找不到文件内容，file_id: {file_id}, 键: {key}")
      # 尝试备用键名
      alternate_key = f"sx_jiliang_records:file:{file_id}"
      alternate_content = redis_client.get(alternate_key)
      if alternate_content:
        logger.info(f"使用备用键找到文件内容，file_id: {file_id}, 键: {alternate_key}, 长度: {len(alternate_content)}")
        return alternate_content
    else:
      logger.info(f"从Redis获取到文件内容，file_id: {file_id}, 键: {key}, 长度: {len(content)}")
    
    return content
  except Exception as e:
    logger.error(f"获取文件内容时发生错误: {file_id}, {str(e)}", exc_info=True)
    return None

# @router.post("/jiliang/file_chat/v2")
# async def chat_stream(request: Request, body: FileQARequest):
#     header = dict(request.headers)
#     apikey = ''
#     userid = ''
#     uri = ''
#     if 'x-api-key' in header:
#         apikey = header['x-api-key']
#     if 'x-username' in header:
#         userid = header['x-username']
#     if 'x-uri' in header:
#         uri = header['x-uri']
#     if not uri:
#         uri = request.url.path
#     logger.info(f"Header: {header}")
#     logger.info(f"RequestURI: {uri}")
#     logger.info(f"Username: {userid}")
#     logger.info(f"Api-Key: {apikey}")
#
#     qa_id = body.qa_id
#     if qa_id is None:
#         qa_id = str(uuid4())
#     key = f"{redis_table}:{qa_id}"
#     his = redis_client.get(key)
#     if his is None:
#         history = sys_prompt[:]
#         file_text = get_file_text(body.file_id)
#         if file_text:
#             history.append({"role": "system", "content": f"这是用户上传的文件，你可以参考文件内容回答用户问题：{file_text}"})
#     else:
#         history = json.loads(his)
#     headers = {"Qa-Id": qa_id}
#     history.append({"role": "user", "content": body.question})
#     return StreamingResponse(common_chat(body.question, history, qa_id, True, userid, apikey, uri), media_type="text/event-stream", headers=headers)


# @router.get("/jiliang/download_file/{file_id}")
# async def download_file(file_id: str):
#   """
#   下载处理后的文件内容
#
#   Args:
#       file_id: 文件ID
#
#   Returns:
#       文件下载响应
#   """
#   try:
#     # 从Redis获取文件内容
#     key = f"{redis_table}:file:{file_id}"
#     content = redis_client.get(key)
#
#     if content is None:
#       # 尝试备用键名
#       alternate_key = f"sx_jiliang_records:file:{file_id}"
#       content = redis_client.get(alternate_key)
#
#     if content is None:
#       return R.error('404', '找不到文件内容或文件内容已过期')
#
#     # 使用简单的文件名，避免编码问题
#     safe_filename = f"file_{file_id}.txt"
#
#     # 返回TXT格式
#     response = StreamingResponse(
#       BytesIO(content.encode('utf-8')),
#       media_type="text/plain"
#     )
#
#     # 使用URL编码处理Content-Disposition中的文件名
#     import urllib.parse
#     encoded_filename = urllib.parse.quote(safe_filename)
#     response.headers["Content-Disposition"] = f"attachment; filename={encoded_filename}"
#
#     return response
#
#   except Exception as e:
#     logger.error(f"下载文件时发生错误: {e}", exc_info=True)
#     return R.error('500', f'下载文件时发生错误: {str(e)}')


@router.post("/jiliang/file_chat")
async def unified_chat_stream(request: Request, body: FileQARequest):
  """
  统一问答接口入口，支持功能统计

  支持的问答类型：
  - qa_type = 0: 知识库问答
  - qa_type = 1: 文件问答 (默认)
  - qa_type = 2: 图片问答

  Args:
      request: FastAPI请求对象
      body: 文件问答请求体

  Returns:
      StreamingResponse: 流式响应
  """
  # 提取请求头信息
  header = dict(request.headers)
  apikey = header.get('x-api-key', '')
  userid = header.get('x-username', '')
  uri = header.get('x-uri', '') or request.url.path
  
  logger.info(f"Jiliang Unified Chat开始处理请求")
  logger.info(f"User: {userid}, API-Key: {apikey[:20]}..., URI: {uri}")
  logger.info(f"Function: {body.function_type}, QA_Type: {body.qa_type}")
  logger.info(f"Question: {body.question[:100]}...")
  
  try:
    # 确定问答类型，优先使用 qa_type，如果没有则使用 chat_type，都没有则默认为1（文件问答）
    qa_type = body.qa_type if body.qa_type is not None else body.chat_type
    
    # 如果qa_type仍然为None或不在支持的类型中，则设置为默认值1（文件问答）
    if qa_type is None or qa_type not in [0, 1, 2]:
      qa_type = 1  # 默认使用文件问答
      logger.info(f"qa_type未提供或不支持，设置为默认值: {qa_type} (文件问答)")
    
    logger.info(f"最终使用的问答类型: {qa_type}")
    
    # 对于图片问答，强制使用专用的图片问答模型
    if qa_type == 2:
      body.model = image_qa_model
      logger.info(f"图片问答强制使用专用模型: {body.model}")
    else:
      # 处理模型选择逻辑
      if body.model is None or body.model == '':
        if userid is not None and userid != '':
          model_name = query_user_model(userid)
          if model_name is not None and model_name != '':
            body.model = model_name
            logger.info(f"从用户配置获取模型: {body.model}")
      
      # 如果还是没有模型，使用默认模型
      if body.model is None or body.model == '':
        body.model = model
        logger.info(f"使用默认模型: {body.model}")
    
    # 处理温度参数
    temperature = body.temperature if body.temperature is not None else 0.6
    logger.info(f"Temperature: {temperature}")
    
    # 生成会话ID
    is_new_conversation = body.qa_id is None or not body.qa_id.strip()
    if is_new_conversation:
      qa_id = str(uuid4())
      logger.info(f"未提供有效qa_id，创建新会话: {qa_id}")
    else:
      qa_id = body.qa_id
      logger.info(f"使用现有会话ID: {qa_id}")
    
    # 根据问答类型获取系统提示
    cur_prompt = get_sys_prompt(body.model)
    if cur_prompt is None or cur_prompt == '':
      cur_prompt = sys_prompt
    
    # 处理历史记录
    if is_new_conversation:
      history = cur_prompt[:]
      logger.info("创建新对话历史")
    else:
      key = f"{redis_table}:{qa_id}"
      his = redis_client.get(key)
      if his is None:
        logger.warning(f"提供的qa_id: {qa_id} 未找到对应历史记录，将作为新会话处理")
        history = cur_prompt[:]
      else:
        history = json.loads(his)
        logger.info(f"加载会话历史，qa_id: {qa_id}, 历史记录条数: {len(history)}")
    
    # 构建响应头
    response_headers = {
      "Qa-Id": qa_id,
      AI_ASSISTANT_HEADER: AI_ASSISTANT_WARN_URLENCODER
    }
    
    # 如果提供了功能类型但没有功能名称，自动查询
    if body.function_type and not body.function_name:
      func_info = SimplifiedStatsService._get_function_info(body.function_type)
      if func_info:
        body.function_name = func_info['function_name']
        logger.info(f"自动查询功能名称: {body.function_type} -> {body.function_name}")
    
    # 根据问答类型处理请求
    if qa_type == 0:
      # 知识库问答
      logger.info("转发到知识库问答")
      return StreamingResponse(
        content=rag_chat(
          body.question, history, qa_id, body.model, userid, apikey, uri, temperature,
          body.function_type, body.function_name
        ),
        media_type="text/event-stream; charset=utf-8",
        headers=response_headers
      )
    
    elif qa_type == 1:
      # 文件问答（默认类型）
      logger.info("处理文件问答")
      
      # 对于文件问答，如果没有提供文件参数
      if not body.file_id and not (body.file_ids and len(body.file_ids) > 0):
        logger.info("文件问答请求缺少文件参数，但仍然处理请求")
      
      return await handle_file_chat(body, history, qa_id, userid, apikey, uri, temperature, response_headers)
    
    elif qa_type == 2:
      # 图片问答
      logger.info("处理图片问答")
      
      # 验证图片参数
      if not body.image_data and not body.image_url:
        logger.warning("图片问答请求缺少图片参数")
        
        async def warning_stream():
          yield 'data: ' + json.dumps({
            'type': 'error',
            'data': "图片问答需要提供图片数据(image_data)或图片URL(image_url)"
          }, ensure_ascii=False) + '\n\n'
        
        return StreamingResponse(
          content=warning_stream(),
          media_type="text/event-stream",
          headers=response_headers
        )
      
      # 记录请求时间
      request_time = datetime.now()
      
      return StreamingResponse(
        content=image_qa_chat(
          body.question, body.image_data, body.image_url, history, qa_id, body.model,
          userid, apikey, uri, temperature, body.function_type, body.function_name, request_time
        ),
        media_type="text/event-stream",
        headers=response_headers
      )
  
  except Exception as e:
    logger.error(f"统一问答接口处理请求时出错: {e}", exc_info=True)
    
    async def error_stream():
      yield 'data: ' + json.dumps({
        'type': 'error',
        'data': f"处理请求时出错: {str(e)}"
      }, ensure_ascii=False) + '\n\n'
    
    return StreamingResponse(
      content=error_stream(),
      media_type="text/event-stream"
    )


async def handle_file_chat(body: FileQARequest, history, qa_id, userid, apikey, uri, temperature, headers):
  """
  处理文件问答的逻辑，以新文件为主要分析对象，历史记录为辅助

  Args:
      body: 文件问答请求体
      history: 对话历史
      qa_id: 会话ID
      userid: 用户ID
      apikey: API密钥
      uri: 请求URI
      temperature: 温度参数
      headers: 响应头

  Returns:
      StreamingResponse: 流式响应
  """
  try:
    logger.info(f"开始处理文件聊天 - User: {userid}, QA_ID: {qa_id}, Function: {body.function_type}")
    
    # 检查并清除可能存在的空键历史记录
    empty_key = f"{redis_table}:"
    empty_key_history = redis_client.get(empty_key)
    if empty_key_history:
      logger.warning(f"检测到空键历史记录，准备清除: {empty_key}")
      redis_client.delete(empty_key)
    
    # 处理文件内容
    file_content_added = False
    file_count = 0
    current_file_content = ""
    
    # 处理多文件情况
    if body.file_ids and len(body.file_ids) > 0:
      logger.info(f"准备处理多文件内容，文件数量: {len(body.file_ids)}")
      all_file_contents = []
      failed_files = []
      file_count = len(body.file_ids)
      
      for index, file_id in enumerate(body.file_ids):
        logger.info(f"开始处理第{index + 1}个文件，file_id: {file_id}")
        file_text = get_file_text(file_id)
        
        if file_text:
          logger.info(f"成功获取文件内容，file_id: {file_id}, 内容长度: {len(file_text)}")
          all_file_contents.append(f"第{index + 1}个文件的内容：\n{file_text}")
        else:
          logger.warning(f"无法获取文件内容，file_id: {file_id}")
          failed_files.append(file_id)
      
      # 构建当前文件内容
      if all_file_contents:
        current_file_content = "\n\n".join(all_file_contents)
        logger.info(f"合并后的多文件内容总长度: {len(current_file_content)}")
        file_content_added = True
        
        # 如果有失败的文件，添加到内容中
        if failed_files:
          current_file_content += f"\n\n注意：以下文件ID无法读取内容：{', '.join(failed_files)}"
      else:
        logger.error(f"所有文件({len(body.file_ids)}个)均无法读取内容")
        current_file_content = "用户上传的文件无法读取内容，请确认文件是否正确上传或已过期。"
        file_content_added = True
    
    # 处理单文件情况
    elif body.file_id and not file_content_added:
      logger.info(f"处理单文件内容，file_id: {body.file_id}")
      file_text = get_file_text(body.file_id)
      file_count = 1
      
      if file_text:
        logger.info(f"成功获取文件内容，file_id: {body.file_id}, 内容长度: {len(file_text)}")
        current_file_content = file_text
        file_content_added = True
        logger.info("已获取当前文件内容，将作为主要分析对象")
      else:
        logger.error(f"无法获取文件内容，file_id: {body.file_id}")
        current_file_content = "用户上传的文件无法读取内容，请确认文件是否正确上传或已过期。"
        file_content_added = True
    
    # 如果没有提供文件，设置提示
    if not file_content_added:
      logger.warning("文件问答请求中未提供有效的文件ID")
      current_file_content = "用户想要进行文件问答，但未提供有效的文件。请提醒用户先上传文件。"
      file_count = 0
    
    # 以新文件为主，历史为辅
    if file_content_added and current_file_content:
      primary_content_prompt = f"""
【当前主要分析对象】
以下是用户刚刚上传的文件内容，这是本次对话的主要分析对象，请优先基于这些内容回答用户问题：

{current_file_content}

【分析指导】
1. 请主要基于上述文件内容回答用户的问题
2. 如果历史对话中有其他内容（如之前的图片或文件），可以作为补充参考，但不应成为主要回答依据
3. 如果用户问题与当前文件内容相关，请详细分析文件内容
4. 如果用户问题需要结合历史信息，请明确区分当前文件内容和历史信息
"""
      
      history.append({
        "role": "system",
        "content": primary_content_prompt
      })
      logger.info("已添加以新文件为主的系统提示")
    else:
      # 没有文件内容的情况
      history.append({
        "role": "system",
        "content": current_file_content
      })
    
    # 添加用户问题到历史记录
    history.append({"role": "user", "content": body.question})
    logger.info(f"已添加用户问题到对话历史: {body.question[:100]}...")
    
    # 记录请求时间
    request_time = datetime.now()
    
    # 发送聊天请求（传递统计参数）
    return StreamingResponse(
      content=common_chat(
        body.question, history, qa_id, body.model, True, userid, apikey, uri, temperature,
        body.function_type, body.function_name, request_time, file_count
      ),
      media_type="text/event-stream",
      headers=headers
    )
  
  except Exception as e:
    logger.error(f"处理文件聊天时发生异常: {e}", exc_info=True)
    
    # 创建错误响应流
    async def error_stream():
      yield 'data: ' + json.dumps({
        'type': 'error',
        'data': f"处理文件聊天时发生错误: {str(e)}"
      }, ensure_ascii=False) + '\n\n'
    
    return StreamingResponse(
      content=error_stream(),
      media_type="text/event-stream",
      headers=headers
    )


@router.post("/jiliang/qa_records", response_model=ApiResponse)
async def query_qa_records(request: QARecordQueryRequest):
    """分页查询问答记录接口"""
    try:
        with DMDatabase(**dm_config) as db:
            # 参数校验
            if not request.userid or request.chat_type not in (0, 1):
                return ApiResponse(
                    code="500",
                    msg="参数校验失败",
                    errorCode="1001",
                    errorMsg="参数错误",
                    data=None
                )

            # 计算分页参数
            page = max(1, request.page)
            page_size = min(100, max(1, request.page_size))
            start = (page - 1) * page_size + 1
            end = page * page_size
      
            conditions = ['"userid" = :userid', '"CHAT_TYPE" = :chat_type']
            params = {"userid": request.userid, "chat_type": request.chat_type}
      
            if request.qa_id:
              conditions.append('"QA_ID" = :qa_id')
              params["qa_id"] = request.qa_id
            
            where_clause = " AND ".join(conditions)
      
            # 查询总数
            count_sql = f"""
                SELECT COUNT(*) AS total
                FROM "SX_AIPLATFORM"."sx_jiliang_records"
                WHERE {where_clause}
            """
            total_result = db.execute_query(count_sql, params)
            total = total_result[0]['total']

            if total == 0:
                return ApiResponse(
                    data={
                        "records": [],
                        "total": 0,
                        "page": page,
                        "page_size": page_size
                    }
                )

            # 分页查询数据
            data_sql = f"""
                SELECT * FROM (
                    SELECT ROW_NUMBER() OVER (ORDER BY "createtime" DESC) AS rn,
                        "id", "answer", "question", "reasoning",
                        "createtime", "CHAT_TYPE", "userid", "apikey", "QA_ID"
                    FROM "SX_AIPLATFORM"."sx_jiliang_records"
                    WHERE {where_clause}
                ) WHERE rn BETWEEN :start AND :end
            """
      
            # 添加分页参数
            params["start"] = start
            params["end"] = end
            
            records = db.execute_query(data_sql, params)

            # 处理时间格式
            for record in records:
                if record["createtime"]:
                    record["createtime"] = record["createtime"].strftime("%Y-%m-%d %H:%M:%S")

            return ApiResponse(
                data={
                    "records": [QARecordResponse(**r).dict() for r in records],
                    "total": total,
                    "page": page,
                    "page_size": page_size
                }
            )

    except Exception as e:
        return ApiResponse(
            code="500",
            msg="服务器内部错误",
            errorCode="5001",
            errorMsg="数据库操作异常",
            exception=str(e)
        )

@router.post("/jiliang/qa_session_records", response_model=ApiResponse)
async def query_qa_session_records(request: SessionQueryRequest):
  """查询用户所有会话记录接口"""
  try:
    with DMDatabase(**dm_config) as db:
      # 参数校验
      if not request.userid:
        return ApiResponse(
          code="500",
          msg="参数校验失败",
          errorCode="1001",
          errorMsg="用户ID不能为空",
          data=None
        )
      
      # 计算分页参数
      page = max(1, request.page)
      page_size = min(100, max(1, request.page_size))
      start = (page - 1) * page_size + 1
      end = page * page_size
      
      # 查询qa_id数量
      count_sql = """
                SELECT COUNT(DISTINCT "QA_ID") AS total
                FROM "SX_AIPLATFORM"."sx_jiliang_records"
                WHERE "userid" = :1 AND "QA_ID" IS NOT NULL
            """
      total_result = db.execute_query(count_sql, (request.userid,))
      total = total_result[0]['total']
      
      if total == 0:
        return ApiResponse(
          data={
            "sessions": [],
            "total": 0,
            "page": page,
            "page_size": page_size
          }
        )
      
      # 获取每个qa_id的第一条记录（按时间排序）
      data_sql = """
                SELECT * FROM (
                    SELECT ROW_NUMBER() OVER (ORDER BY MIN("createtime") DESC) AS rn,
                           "QA_ID" as qa_id,
                           MIN("question") KEEP (DENSE_RANK FIRST ORDER BY "createtime") as question,
                           TO_CHAR(MIN("createtime"), 'YYYY-MM-DD HH24:MI:SS') as createtime
                    FROM "SX_AIPLATFORM"."sx_jiliang_records"
                    WHERE "userid" = :1 AND "QA_ID" IS NOT NULL
                    GROUP BY "QA_ID"
                ) WHERE rn BETWEEN :2 AND :3
            """
      
      sessions = db.execute_query(data_sql, (
        request.userid,
        start,
        end
      ))
      
      return ApiResponse(
        data={
          "sessions": sessions,
          "total": total,
          "page": page,
          "page_size": page_size
        }
      )
  
  except Exception as e:
    return ApiResponse(
      code="500",
      msg="服务器内部错误",
      errorCode="5001",
      errorMsg="数据库操作异常",
      exception=str(e)
    )


@router.get("/jiliang/stats/functions")
async def get_jiliang_function_stats(request: Request,
                                     function_type: str = None,
                                     start_date: str = None,
                                     end_date: str = None):
  """获取吉量功能调用统计"""
  try:
    stats = StatsQueryService.get_function_stats_from_existing_tables(
      'SX_JILIANG_RECORDS', start_date, end_date, function_type
    )
    return R.ok({"functions": stats})
  except Exception as e:
    logger.error(f"查询吉量功能统计失败: {e}")
    return R.error('500', f"查询失败: {str(e)}")


@router.get("/jiliang/stats/users/{user_id}")
async def get_jiliang_user_stats(request: Request,
                                 user_id: str,
                                 start_date: str = None,
                                 end_date: str = None):
  """获取吉量用户功能使用统计"""
  header = dict(request.headers)
  request_user = header.get('x-username', '')
  
  # 权限验证：只能查询自己的统计
  if request_user != user_id:
    return R.error('403', "权限验证失败")
  
  try:
    stats = StatsQueryService.get_user_function_stats(
      user_id, 'SX_JILIANG_RECORDS', start_date, end_date
    )
    return R.ok({"user_stats": stats})
  except Exception as e:
    logger.error(f"查询吉量用户统计失败: {e}")
    return R.error('500', f"查询失败: {str(e)}")


@router.get("/jiliang/stats/config/functions")
async def get_jiliang_function_config():
  """获取吉量功能类型配置"""
  try:
    with DMDatabase(**dm_config) as db:
      sql = """
            SELECT "FUNCTION_TYPE", "FUNCTION_NAME", "API_PATH", "CATEGORY", "DESCRIPTION"
            FROM "SX_AIPLATFORM"."T_FUNCTION_TYPE_CONFIG"
            WHERE ("API_PATH" LIKE '/jiliang/%' OR "API_PATH" = '/wps/chat') AND "IS_ACTIVE" = '1'
            ORDER BY "CATEGORY", "FUNCTION_TYPE"
            """
      result = db.execute_query(sql)
      return R.ok({"functions": result})
  except Exception as e:
    logger.error(f"获取吉量功能配置失败: {e}")
    return R.error('500', f"获取功能配置失败: {str(e)}")

app = FastAPI()
# 设置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头
)
app.include_router(router, prefix="")
app.include_router(jiliang_translate.router, prefix="")
app.include_router(jiliang_seek.router, prefix="")
app.include_router(asr_router, prefix="")  # ASR语音识别
app.include_router(table_router, prefix="")  # 表格识别
app.include_router(handwriting_router, prefix="")  # 手写识别
app.include_router(dm_ctrl.router, prefix="")  # 数据库
app.include_router(pdf_translate_router, prefix="")  # PDF翻译
app.include_router(jiliang_chat_refactored_router, prefix="")  # 改进版聊天

# 添加应用启动和关闭事件
@app.on_event("startup")
async def startup_event():
  logger.info("应用启动，初始化资源...")
  # 确保临时目录存在
  os.makedirs(PUT_IN_DIR, exist_ok=True)
  os.makedirs(PUT_OUT_DIR, exist_ok=True)
  
  # 预热进程池
  try:
    executor = process_pool_manager.get_executor()
    logger.info("进程池预热完成")
  except Exception as e:
    logger.warning(f"进程池预热失败: {e}")

@app.on_event("shutdown")
async def shutdown_event():
  logger.info("应用关闭，释放资源...")
  
  # 等待运行的任务一些时间完成
  await asyncio.sleep(2)
  
  # 关闭执行器
  shutdown_executors()
  
  logger.info("应用关闭完成")

if __name__ == "__main__":
    # 使用gunicorn启动生产环境，性能更好
    import subprocess
    import sys
    import multiprocessing
    
    # 计算最优worker数量
    worker_count = min(multiprocessing.cpu_count() + 1, 4)
    
    # gunicorn生产环境配置
    gunicorn_cmd = [
        sys.executable, "-m", "gunicorn",
        "rag_chat.jiliang_chat:app",
        "--workers", str(worker_count),
        "--worker-class", "uvicorn.workers.UvicornWorker",
        "--bind", "0.0.0.0:18800",
        "--timeout", "300",                  # 多智能体处理时间长
        "--keep-alive", "5",                 # 保持连接降低开销
        "--max-requests", "250",             # 防止内存泄漏
        "--max-requests-jitter", "15",       # 避免同时重启
        "--worker-tmp-dir", "/dev/shm",      # 使用共享内存
        "--log-level", "info",
        "--access-logfile", "-",             # 访问日志到stdout
        "--error-logfile", "-"               # 错误日志到stderr
    ]
    
    try:
        subprocess.run(gunicorn_cmd)
    except KeyboardInterrupt:
        logger.info("服务被用户中断")
    except FileNotFoundError:
        # 如果没有gunicorn，回退到uvicorn
        logger.warning("未找到gunicorn，使用uvicorn启动")
        uvicorn.run(
            app=app,
            host='0.0.0.0',
            port=18800,
            log_level="info"
        )
