services:
  sx-jiliang-chat-v2:
    image: rag_chat:v1.1.15
    container_name: sx-jiliang-chat-v2
    shm_size: '4gb'                 # LLM推理和PyTorch共享内存
    mem_limit: 16g                  # 4个worker * 4GB = 16GB
    mem_reservation: 6g             # 预留6GB基础内存
    mem_swappiness: 10              # 减少swap使用
    cpus: '6.0'                     # 匹配CPU核心数
    restart: always
    working_dir: /project
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
      nproc: 65536                  # 增加进程限制
    ports:
      - "18802:18800"
      #- "18805:18800"
    volumes:
      - ./:/project
    environment:
      - PYTHONPATH=/project:/project/rag_chat
    command: python rag_chat/jiliang_chat.py

  # sx-wps-chat-v2:
  #   image: python-v2:1.0
  #   container_name: sx-wps-chat-v2
  #   shm_size: '4gb'
  #   mem_limit: 2g
  #   restart: always
  #   working_dir: /project
  #   ports:
  #     - "18801:18801"
  #     #- "18804:18801"
  #   volumes:
  #     - ./:/project
  #   command: python rag_chat/wps_chat.py