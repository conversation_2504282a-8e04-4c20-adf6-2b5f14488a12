services:
  sx-jiliang-chat-v2:
    image: rag_chat:v1.1.15
    container_name: sx-jiliang-chat-v2
    # 内存配置 - 针对多智能体系统优化
    shm_size: '8gb'                 # 增加共享内存，支持多个LLM模型并发
    mem_limit: 26g                  # 充分利用30GB内存，为系统保留4GB
    mem_reservation: 10g            # 预留10GB基础内存（4个worker + 缓存）
    mem_swappiness: 5               # 进一步减少swap使用

    # CPU配置 - 为多智能体并发优化
    cpus: '6.0'                     # 使用6核，为系统保留2核
    cpu_shares: 1024                # 高优先级CPU调度

    # 网络和存储优化
    restart: always
    working_dir: /project

    # 系统限制优化
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
      nproc: 32768                  # 适当调整进程限制
      memlock:                      # 允许内存锁定，提升性能
        soft: -1
        hard: -1

    # 端口映射
    ports:
      - "18802:18800"
      #- "18805:18800"

    # 卷挂载
    volumes:
      - ./:/project
      - /dev/shm:/dev/shm           # 挂载共享内存，提升I/O性能

    # 环境变量 - 多智能体系统优化
    environment:
      - PYTHONPATH=/project:/project/rag_chat
    command: python rag_chat/jiliang_chat.py

  # sx-wps-chat-v2:
  #   image: python-v2:1.0
  #   container_name: sx-wps-chat-v2
  #   shm_size: '4gb'
  #   mem_limit: 2g
  #   restart: always
  #   working_dir: /project
  #   ports:
  #     - "18801:18801"
  #     #- "18804:18801"
  #   volumes:
  #     - ./:/project
  #   command: python rag_chat/wps_chat.py