#!/usr/bin/env python3
"""
吉量聊天重构版API启动脚本
启动jiliang_chat_refactored.py中的API服务
"""

import os
import sys
import uvicorn
from pathlib import Path
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 设置项目路径
project_root = Path(__file__).parent
rag_chat_path = project_root / "rag_chat"
sys.path.insert(0, str(rag_chat_path))

# 导入聊天API路由
from jiliang_chat_refactored import router as chat_router
from jiliang_chat_refactored import initialize_checkpointer, initialize_mcp_router

def create_app():
    """创建FastAPI应用"""
    
    app = FastAPI(
        title="吉量聊天重构版API",
        description="基于LangGraph的聊天服务",
        version="3.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # 添加CORS支持
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 注册聊天路由
    app.include_router(chat_router, prefix="", tags=["聊天"])
    
    @app.get("/")
    async def root():
        """根路径"""
        return {
            "message": "吉量聊天重构版API服务",
            "version": "3.0.0",
            "docs": "/docs",
            "endpoints": {
                "chat_v2": "/jiliang/chat/v2",
                "chat_v3": "/jiliang/chat/v3",
                "list_mcp_tools": "/jiliang/chat/ListMcpTools"
            }
        }
    
    @app.get("/health")
    async def health_check():
        """健康检查"""
        return {"status": "healthy", "service": "jiliang_chat_refactored"}
    
    @app.on_event("startup")
    async def startup_event():
        """应用启动事件"""
        print("初始化checkpointer...")
        await initialize_checkpointer()
        print("初始化MCP路由器...")
        await initialize_mcp_router()
        print("应用启动完成")
    
    return app

def main():
    """主函数"""
    
    # 检查环境变量
    required_vars = ["OPENAI_API_KEY", "OPENAI_BASE_URL", "OPENAI_MODEL"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"警告: 缺少环境变量 {missing_vars}")
        print("请确保在.env文件中配置了相关的AI服务配置")
    
    # 检查必要的目录
    temp_dir = os.getenv('TEMP_DIR', os.path.join(os.path.expanduser("~"), "temp"))
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir, exist_ok=True)
        print(f"创建临时目录: {temp_dir}")
    
    # 选择端口
    port = 18800
    
    print("启动吉量聊天重构版API服务...")
    print("=" * 50)
    print("服务信息:")
    print(f"  - 地址: http://localhost:{port}")
    print(f"  - API文档: http://localhost:{port}/docs")
    print(f"  - 健康检查: http://localhost:{port}/health")
    print(f"  - 聊天接口: http://localhost:{port}/jiliang/chat/v2")
    print(f"  - MCP工具接口: http://localhost:{port}/jiliang/chat/ListMcpTools")
    print("=" * 50)
    
    # 创建应用
    app = create_app()
    
    # 启动服务
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=port,
        log_level="info",
        reload=False,
        access_log=True,
        # 流式响应优化参数
        timeout_keep_alive=65,  # 保持连接65秒，支持长时间流式响应
        limit_concurrency=1000,  # 增加并发限制
        backlog=2048,  # 增加连接队列大小
        h11_max_incomplete_event_size=16 * 1024,  # 增加事件大小限制
    )

if __name__ == "__main__":
    main()